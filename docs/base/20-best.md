# 最佳实践

新项目使用 `base` 模板，可选 `tabbar` 模板。如果需要多语言，可以选 `i18n` 模板。

同时参考 `demo` 模板，可以直接 `clone` `demo` 项目，用来参考用。

![unibest templates](https://oss.laf.run/ukw0y1-site/xmind/unibest模板.png)

## 创建项目

推荐使用 `pnpm` :

```sh
# 新项目创建
pnpm create unibest my-project -t base
```

## DEMO 模板

`demo` 模版-在线地址：<https://feige996.github.io/hello-unibest/>

推荐先全部体验一下 `demo` 的示例

## 必看章节

- [介绍](/base/1-introduction)
- [快速开始](/base/2-start)
- [uni 插件](/base/3-plugin)
- [常见问题](/base/14-faq)
- [常见问题 2](/base/15-faq)
- [运行发布](/base/11-build)
