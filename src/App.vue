<script setup lang="ts">
import { onLaunch, onShow, onHide } from '@dcloudio/uni-app'
import 'abortcontroller-polyfill/dist/abortcontroller-polyfill-only'
import { useUserStore } from '@/store'

onLaunch(() => {
  const userStore = useUserStore()
  if (userStore.isLogin) {
    userStore.getUserInfo()
  } else {
    userStore.logout()
  }
})
onShow(() => {})
onHide(() => {})
</script>

<style lang="scss">
.quizPutTag {
  display: flex;
  padding: 0 20px;
  border-bottom: 1px solid #333;
}
._block {
  display: flex !important;
  flex-wrap: wrap;
  align-items: center;
}
</style>
