// 全局要用的类型放到这里

declare global {
  type IResData<T> = {
    code: number
    msg: string
    data: T
  }

  // uni.uploadFile文件上传参数
  type IUniUploadFileOptions = {
    file?: File
    files?: UniApp.UploadFileOptionFiles[]
    filePath?: string
    name?: string
    formData?: any
  }

  type IUserInfo = {
    avatar: string | null
    boardingStatus: string | null
    cityCode: string | null
    classSchool: string | null
    createTime: string
    currSchool: string | null
    currSchoolName: string | null
    deptId: string
    deptName: string
    districtCode: string | null
    email: string
    endTime: string
    grade: string
    homeAddress: string | null
    jyeooCode: string
    loginDate: string
    loginIp: string
    memberType: string
    nickName: string
    parentOpenid: string | null
    parentPhoneFirst: string
    parentPhoneSecond: string | null
    phonenumber: string
    postIds: string | null
    provinceCode: string | null
    remark: string | null
    roleId: string | null
    roleIds: string | null
    roles: string | null
    sciences: string | null
    sex: string
    source: string
    startTime: string
    status: string
    studentNum: string | null
    studyMentorId: number
    studyMentorName: string
    tenantId: string
    userId: string
    userName: string
    userType: string
  }
  type IAuthInfo = {
    token?: string
    clientId?: string
    expireTime?: number
  }

  type ICourseItem = {
    courseId: string
    courseName: string
    description: string
    detailImage: string
    isActivated: number
    listImage: string
    recommendImage: string
    recommendStatus: string
    resourceVoList: IResourceItem[] | null
    subTitle: string
    subjects: any[] | null
    typeId: string
    typeName: string | null
    viewRemainTip: string | null
    viewTip: string | null
  }
  type IClassTypeItem = {
    typeId: string
    typeName: string
  }
  type IFavoriteFoldItem = {
    foldeId: string
    foldeName: string
    resourceVos: any[] | null
    userId: string
  }
  type IResourceItem = {
    courseExercises: string
    courseId: string
    courseNotes: string
    courseSyllabus: string | null
    orderNum: number
    pointExpandedKeys: string[] | null
    pointIdList: string[] | null
    pointIds: string
    pointNo: string
    resourceId: string
    subjectCode: string
    subjectId: number
    subjectName: string
    teacherId: string
    teacherName: string
    typeName: string | null
    versionId: string
    versionName: string
    videoCourseLink: string
    videoMinute: number
    videoResourceName: string
    videoSecond: number
    collectStatus: 1 | 0
    viewMinute: number
    viewSecond: number
    detailImage: string
  }
  type ISubjectItem = {
    subjectId: string
    subjectName: string
    versions?: IVersionItem[]
  }
  type IVersionItem = {
    versionId: string
    versionName: string
    teachers?: ITeacherItem[]
  }
  type ITeacherItem = {
    teacherId: string
    teacherName: string
  }
  type IMistakeItem = {
    applyId: string
    subjectId: string
    paperId: string
    content: string
    createTime: string
  }
  type IPlanItem = {
    currentMonth: boolean
    date: string
    day: string
    plans: IPlanDetailItem[]
  }
  type IPlanDetailItem = {
    attendanceStatus: string | null
    completStatus: number
    contentInfo: string | null
    deptId: string
    endTime: string
    grade: string
    planDate: string
    planId: string
    planShowStr: string
    planShowStrShort: string | null
    resourceId: string
    startTime: string
    status: string
    studyContent: string
    studyMentorId: number
    studyMentorName: string
    studyOrder: string | null
    subjectCode: string | null
    subjectId: number
    subjectName: string | null
    userId: string
    userName: string
    videoMinute: number
    videoSecond: number
  }
}

export {} // 防止模块污染
