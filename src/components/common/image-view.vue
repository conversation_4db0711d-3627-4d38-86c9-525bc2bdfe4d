<template>
  <uv-image
    :src="realSrc"
    :width="width"
    :mode="mode"
    :height="height"
    :shape="shape"
    :radius="radius"
  >
    <template v-slot:error>
      <view class="text-black/60 text-xs">加载失败</view>
    </template>
  </uv-image>
</template>

<script lang="ts" setup>
import { getListOssByIds } from '@/service'

const props = defineProps({
  src: {
    type: String,
    default: '',
  },
  shape: {
    type: String,
    default: 'square',
  },
  mode: {
    type: String,
    default: 'aspectFill',
  },
  width: {
    type: String,
    default: '100%',
  },
  height: {
    type: String,
    default: '100%',
  },
  radius: {
    type: String,
    default: '0',
  },
})
const realSrc = ref('')
const imageTitle = ref('')

watch(
  () => props.src,
  async (val) => {
    if (val) {
      const res = await getListOssByIds(val)
      if (res.data && res.data[0]) {
        imageTitle.value = res.data[0].originalName
        realSrc.value = res.data[0].url
      }
    } else {
      imageTitle.value = ''
      realSrc.value = ''
    }
  },
  { deep: true, immediate: true },
)
</script>
