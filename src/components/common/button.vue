<template>
  <button
    :disabled="disabled || loading"
    :loading="loading"
    :size="size"
    :class="['clever-button', className, type]"
  >
    <slot></slot>
  </button>
</template>
<script setup>
defineProps({
  type: {
    type: String,
    default: 'default',
  },
  size: {
    type: String,
    default: 'default',
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  loading: {
    type: Boolean,
    default: false,
  },
  className: {
    type: String,
    default: '',
  },
})
</script>
<style lang="scss" scoped>
.clever-button {
  @apply text-white px-[40px] mx-0 rounded-full h-[56px] flex items-center justify-center text-xl hover:opacity-80;
  background: #e5e5e5;
  color: inherit;
  outline: none;
  &:after {
    content: none;
  }
  &.primary {
    background: $uv-primary;
    color: #fff;
  }
  &.error {
    background: $uv-error;
    color: #fff;
  }
  &.warning {
    background: $uv-warning;
    color: #fff;
  }
  &.success {
    background: $uv-success;
    color: #fff;
  }
  &[size='small'] {
    height: 40px;
    font-size: 16px;
  }
  &[disabled] {
    cursor: not-allowed;
    opacity: 0.8;
    &:hover {
      opacity: 0.8;
    }
  }
}
</style>
