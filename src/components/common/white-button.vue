<template>
  <view
    class="white-btn bg-white h-[36px] flex items-center rounded-full pl-2 pr-4 cursor-pointer text-black/60"
  >
    <view
      class="size-[20px] rounded-full flex items-center justify-center"
      :style="{
        backgroundColor: iconBg,
      }"
    >
      <text :class="`iconfont ${icon} text-[12px] text-white`"></text>
    </view>
    <text class="ml-2">{{ text }}</text>
  </view>
</template>
<script setup>
defineProps({
  text: {
    type: String,
    default: '设置',
  },
  icon: {
    type: String,
    default: 'icon-setting',
  },
  iconBg: {
    type: String,
    default: '#BFBFBF',
  },
})
</script>
<style lang="scss" scoped>
.white-btn {
  &:hover {
    background: $uv-primary;
    color: #fff;
  }
}
</style>
