<template>
  <uv-popup
    ref="popupRef"
    mode="center"
    :close-on-click-overlay="false"
    custom-style="background: none; overflow: visible;"
  >
    <view class="modal confirm-modal" :style="{ width: width }">
      <view class="modal-close" @click="close" v-if="showClose">
        <text class="iconfont icon-close text-white text-[32px]"></text>
      </view>
      <view class="modal-title">{{ title }}</view>
      <view class="modal-content">
        <slot></slot>
      </view>
      <view class="modal-footer">
        <c-button shape="circle" type="default" @click="handleCancel" style="width: 180px">
          {{ cancelText }}
        </c-button>
        <c-button
          shape="circle"
          :loading="loading"
          type="primary"
          @click="handleConfirm"
          style="width: 180px"
        >
          {{ confirmText }}
        </c-button>
      </view>
    </view>
  </uv-popup>
</template>

<script lang="ts" setup>
import CButton from '@/components/common/button.vue'
defineOptions({
  name: 'Modal',
})

defineProps({
  title: {
    type: String,
    default: '提示',
  },
  showClose: {
    type: Boolean,
    default: true,
  },
  width: {
    type: String,
    default: '530px',
  },
  loading: {
    type: Boolean,
    default: false,
  },
  confirmText: {
    type: String,
    default: '确定',
  },
  cancelText: {
    type: String,
    default: '取消',
  },
})
const emit = defineEmits(['confirm', 'cancel', 'showImage'])
const popupRef = ref(null)
const open = () => {
  popupRef.value.open()
}
const close = () => {
  console.log('关闭了')
  emit('showImage')
  popupRef.value.close()
}
defineExpose({
  open,
  close,
})

const handleConfirm = async () => {
  emit('confirm')
}

const handleCancel = () => {
  emit('cancel')
}
</script>

<style lang="scss" scoped></style>
