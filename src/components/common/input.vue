<template>
  <view class="input-wrapper" :class="{ 'is-focus': isFocus }">
    <slot name="prefix"></slot>
    <input
      :type="type"
      :value="value"
      placeholder-class="placeholder"
      :placeholder="placeholder"
      :clearable="clearable"
      :password="password"
      class="input"
      @focus="handleFocus"
      @blur="handleBlur"
      @input="handleInput"
    />
    <slot name="suffix"></slot>
  </view>
</template>
<script setup>
defineProps({
  value: {
    type: String,
    default: '',
  },
  type: {
    type: String,
    default: 'text',
  },
  placeholder: {
    type: String,
    default: '',
  },
  clearable: {
    type: Boolean,
    default: false,
  },
  password: {
    type: Boolean,
    default: false,
  },
})
const model = defineModel()
const emit = defineEmits(['focus', 'blur', 'input'])
const isFocus = ref(false)
const handleFocus = (e) => {
  emit('focus', e)
  isFocus.value = true
}
const handleBlur = (e) => {
  emit('blur', e)
  isFocus.value = false
}
const handleInput = (e) => {
  console.log('handleInput', e.detail.value)
  emit('input', e.detail.value)
  model.value = e.detail.value
}
</script>
<style lang="scss" scoped>
.input-wrapper {
  @apply flex items-center justify-center w-full h-[56px] border border-[#ccc] border-solid rounded-full bg-white px-5;
  &.is-focus {
    border-color: $uv-primary;
  }
}
.input {
  @apply flex-1 p-4 rounded-full h-full border-none;
}
.placeholder {
  color: #b2b2b2;
}
</style>
