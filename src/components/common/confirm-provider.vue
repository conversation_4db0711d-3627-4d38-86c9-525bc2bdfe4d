<!-- src/components/ConfirmProvider.vue -->
<template>
  <uv-popup
    ref="popup"
    mode="center"
    :close-on-click-overlay="false"
    @change="onPopupChange"
    custom-style="background: none;"
  >
    <!-- 弹窗内部自定义内容 -->
    <view class="modal confirm-modal">
      <view class="modal-title">
        {{ state.title }}
      </view>
      <view class="modal-content">
        {{ state.message }}
      </view>
      <view class="modal-footer">
        <c-button
          v-if="state.showCancel"
          shape="circle"
          type="default"
          size="small"
          @click="handleCancel"
        >
          {{ state.cancelText }}
        </c-button>
        <c-button shape="circle" type="primary" size="small" @click="handleConfirm">
          {{ state.confirmText }}
        </c-button>
      </view>
    </view>
  </uv-popup>
</template>

<script setup lang="ts">
import CButton from '@/components/common/button.vue'
import { watch, ref } from 'vue'
import { useConfirmState } from '@/hooks/useConfirm'
const { visible, state, handleConfirm, handleCancel } = useConfirmState()
const popup = ref(null)

const customStyle = { width: '150px', height: '40px' }

// 根据 visible 打开或关闭弹窗
watch(visible, (show) => {
  console.log('visible change', show)
  if (show)
    popup.value.open() // 打开弹层 :contentReference[oaicite:0]{index=0}
  else popup.value.close() // 关闭弹层
})

// @change 事件也能同步 visible（可选）
function onPopupChange(e) {
  if (!e.show) visible.value = false
}
</script>
