<template>
  <view class="cate-tab">
    <view
      v-for="(item, index) in list"
      :key="index"
      class="cate-item cursor-pointer"
      :class="{ active: activeCate === item.typeId }"
      @click="handleCateChange(item)"
    >
      {{ item.typeName }}
    </view>
  </view>
</template>
<script setup lang="ts">
defineProps({
  list: {
    type: Array<IClassTypeItem>,
    default: () => [],
  },
  activeCate: {
    type: String,
    default: '',
  },
})
const emit = defineEmits(['change'])
const handleCateChange = (item) => {
  emit('change', item)
}
</script>
<style lang="scss" scoped></style>
