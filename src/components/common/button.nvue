<template>
  <button
    :disabled="disabled || loading"
    :loading="loading"
    :size="size"
    :class="['clever-button', className, type]"
  >
    <slot></slot>
  </button>
</template>
<script setup>
defineProps({
  type: {
    type: String,
    default: 'default',
  },
  size: {
    type: String,
    default: 'default',
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  loading: {
    type: Boolean,
    default: false,
  },
  className: {
    type: String,
    default: '',
  },
})
</script>
<style lang="scss" scoped>
.clever-button {
  color: #fff;
  padding: 0 40px;
  margin-left: 0;
  margin-right: 0;
  border-radius: 999px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  background: #e5e5e5;
  color: inherit;
  outline: none;
  &:after {
    content: none;
  }
  &:hover {
    opacity: 0.8;
  }
  &.primary {
    background: $uv-primary;
    color: #fff;
  }
  &.error {
    background: $uv-error;
    color: #fff;
  }
  &.warning {
    background: $uv-warning;
    color: #fff;
  }
  &.success {
    background: $uv-success;
    color: #fff;
  }
  &[size='small'] {
    height: 40px;
    font-size: 16px;
  }
  &[disabled] {
    cursor: not-allowed;
    opacity: 0.8;
    &:hover {
      opacity: 0.8;
    }
  }
}
</style>
