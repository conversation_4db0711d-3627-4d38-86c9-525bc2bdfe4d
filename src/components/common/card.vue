<template>
  <view :class="['flex flex-col bg-white rounded-[14px] overflow-hidden', className]">
    <view
      v-if="title || subtitle"
      class="h-[60px] flex items-center px-6 border-b border-b-solid border-b-[#ddd]"
    >
      <view class="flex-1 text-xl font-bold">{{ title }}</view>
      <view class="text-base font-normal text-black/60" v-if="subtitle">
        {{ subtitle }}
      </view>
    </view>
    <view :class="['', contentClass]" style="padding: 24px">
      <slot></slot>
    </view>
  </view>
</template>
<script setup>
defineProps({
  title: {
    type: String,
    default: '',
  },
  subtitle: {
    type: String,
    default: '',
  },
  className: {
    type: String,
    default: '',
  },
  contentClass: {
    type: String,
    default: '',
  },
})
</script>
<style lang="scss" scoped></style>
