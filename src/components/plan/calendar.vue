<template>
  <view class="calendar">
    <!-- Header -->
    <view class="calendar-header">
      <view class="nav-btn" @click="prevMonth">
        <text class="iconfont icon-arrow-left"></text>
      </view>
      <text class="month-label">{{ currentYear }}年{{ currentMonth + 1 }}月</text>
      <view class="nav-btn" @click="nextMonth">
        <text class="relative iconfont icon-arrow-right"></text>
      </view>
    </view>

    <!-- Weekday Labels -->
    <view class="calendar-weekdays">
      <text v-for="day in weekDays" :key="day" class="weekday">{{ day }}</text>
    </view>

    <!-- Days Grid -->
    <view class="calendar-grid">
      <view
        v-for="day in days"
        :key="day.key"
        :class="['calendar-cell', { 'other-month': !day.inCurrentMonth, today: day.isToday }]"
        @click="handleDayClick(day)"
      >
        <text class="day-number">{{ day.date.getDate() }}</text>
        <text class="today-tag" v-if="day.isToday">今天</text>
        <view class="events">
          <view
            v-for="evt in eventsMap[day.formatted] || []"
            :key="evt.planId"
            class="event-item"
            @click="handleEventClick(evt)"
          >
            <text class="event-title">{{ evt.planShowStr }}</text>
            <text v-if="evt.completStatus === 1" class="event-status">已完成</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, PropType, watch } from 'vue'

// 事件类型
interface CalendarEvent {
  id: string | number
  title: string
  date: string // 'YYYY-MM-DD'
  completed?: boolean
}

const props = defineProps({
  modelValue: {
    type: String as PropType<string>,
    default: '',
  },
  events: {
    type: Array as PropType<IPlanItem[]>,
    default: () => [],
  },
})

const emit = defineEmits(['eventClick', 'dayClick', 'monthChange'])
// 当前年月
const current = ref(new Date())

const currentYear = computed(() => current.value.getFullYear())
const currentMonth = computed(() => current.value.getMonth())
watch(
  [currentYear, currentMonth],
  () => {
    emit(
      'monthChange',
      `${currentYear.value}-${currentMonth.value + 1 < 10 ? '0' : ''}${currentMonth.value + 1}`,
    )
  },
  { immediate: true },
)

// 周几标签，周一开始
const weekDays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']

// 将事件数组按日期映射
const eventsMap = computed(() => {
  const map: Record<string, IPlanDetailItem[]> = {}
  props.events.forEach((evt) => {
    // if (!map[evt.date]) map[evt.date] = []
    map[evt.date] = evt.plans
  })
  return map
})

// 辅助：格式化日期 YYYY-MM-DD
function formatDate(date: Date) {
  const y = date.getFullYear()
  const m = String(date.getMonth() + 1).padStart(2, '0')
  const d = String(date.getDate()).padStart(2, '0')
  return `${y}-${m}-${d}`
}

// 生成日格
const days = computed(() => {
  const year = current.value.getFullYear()
  const month = current.value.getMonth()

  // 当月天数
  const daysInMonth = new Date(year, month + 1, 0).getDate()
  // 当月第一天星期几 (0=周日)
  const firstWeekDay = new Date(year, month, 1).getDay()
  // 调整周一开始
  const offset = (firstWeekDay + 6) % 7

  // 上月末天数
  const prevDays = new Date(year, month, 0).getDate()

  const totalCells = 42 // 6行
  const arr = [] as Array<{
    key: number
    date: Date
    formatted: string
    inCurrentMonth: boolean
    isToday: boolean
  }>

  for (let i = 0; i < totalCells; i++) {
    let day: number
    let inCurrentMonth = true
    if (i < offset) {
      // 上月
      day = prevDays - offset + 1 + i
      inCurrentMonth = false
    } else if (i < offset + daysInMonth) {
      day = i - offset + 1
    } else {
      // 下月
      day = i - offset - daysInMonth + 1
      inCurrentMonth = false
    }
    const date = new Date(year, month + (inCurrentMonth ? 0 : i < offset ? -1 : 1), day)
    const formatted = formatDate(date)
    const today = formatDate(new Date()) === formatted
    arr.push({ key: i, date, formatted, inCurrentMonth, isToday: today })
  }
  return arr
})

// 翻月
function prevMonth() {
  const dt = new Date(current.value)
  dt.setMonth(dt.getMonth() - 1)
  current.value = dt
}
function nextMonth() {
  const dt = new Date(current.value)
  dt.setMonth(dt.getMonth() + 1)
  current.value = dt
}

const handleEventClick = (evt) => {
  emit('eventClick', evt)
}
const handleDayClick = (day) => {
  emit('dayClick', day)
}
</script>

<style lang="scss" scoped>
.calendar {
  width: 100%;
}
.calendar-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 36px;
  gap: 32px;
}
.nav-btn {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 24px;
  background: #fff;
  cursor: pointer;
  color: #818181;

  &:hover {
    background: $uv-primary;
    color: #fff;
  }
}
.month-label {
  font-size: 28px;
  font-weight: bold;
  margin: 0 20rpx;
}
.calendar-weekdays {
  display: flex;
  align-items: center;
  background: #e2e9f2;
  height: 50px;
  border-radius: 14px 14px 0 0;
}
.weekday {
  flex: 1;
  text-align: center;
  color: #5a5d61;
  font-size: 16px;
}
.calendar-grid {
  display: flex;
  flex-wrap: wrap;
  background: #fff;
  border-radius: 0 0 14px 14px;
}
.calendar-cell {
  width: calc(100% / 7);
  height: 130px;
  border: 1rpx solid #ebedf0;
  padding: 10rpx;
  box-sizing: border-box;
  position: relative;
}
.other-month .day-number {
  color: #ccc;
}
.today {
  border: 1px solid $uv-primary;
}
.day-number {
  position: absolute;
  top: 10px;
  left: 10px;
  font-size: 20px;
}
.today-tag {
  position: absolute;
  top: 10px;
  right: 10px;
  font-size: 14px;
  color: $uv-primary;
  font-weight: 500;
}
.events {
  margin-top: 32px;
  overflow-y: auto;
  overflow-x: hidden;
  height: 92px;
}
.event-item {
  background: #dfecfd;
  border-radius: 4px;
  padding: 0px 4px;
  margin-bottom: 4px;
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #5a5d61;
  cursor: pointer;
}
.event-title {
  flex: 1;
  @apply truncate;
}
.event-status {
  color: $uv-primary;
  margin-left: 8rpx;
  font-size: 12px;
}
</style>
