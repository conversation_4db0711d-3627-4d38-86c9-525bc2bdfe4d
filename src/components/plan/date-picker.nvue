<template>
  <view :id="id" class="date-picker-wrapper">
    <!-- 输入框 -->
    <view class="dp-input" :class="{ disabled }" @click="togglePopup">
      <text class="dp-text" :class="{ placeholder: !selected }">{{ displayValue }}</text>
      <text class="iconfont icon-close text-[12px]" v-if="selected" @click.stop="clear"></text>
      <text class="iconfont icon-down dp-icon"></text>
    </view>
  </view>
  <view v-if="visible" class="dp-overlay" @click="closePopup"></view>
  <view
    v-if="visible"
    class="dp-dropdown"
    :class="alignClass"
    :style="dropdownStyle"
    @click.self="closePopup"
  >
    <!-- 月导航 -->
    <view class="dp-cal-header">
      <text class="dp-nav" @click="prevMonth">&#8249;</text>
      <text class="dp-month-label">{{ currentYear }}-{{ pad(currentMonth + 1) }}</text>
      <text class="dp-nav" @click="nextMonth">&#8250;</text>
    </view>

    <!-- 星期标签 -->
    <view class="dp-weekdays">
      <text v-for="d in weekDays" :key="d" class="dp-weekday">周{{ d }}</text>
    </view>

    <!-- 日期格子 -->
    <view class="dp-days">
      <view
        v-for="day in days"
        :key="day.key"
        class="dp-day"
        :class="{
          other: !day.inCurrent,
          today: day.isToday,
          selected: isSelected(day),
          disabled: props.pickerOptions?.disabledDate && props.pickerOptions.disabledDate(day.date),
        }"
        @click.stop="selectDate(day)"
      >
        <text>{{ day.date.getDate() }}</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onBeforeUnmount } from 'vue'

type PickerOptions = { disabledDate?: (date: Date) => boolean }

const props = defineProps<{
  modelValue: string
  align?: 'left' | 'center' | 'right'
  placeholder?: string
  pickerOptions?: PickerOptions
  disabled?: boolean
}>()
const emit = defineEmits<{
  (e: 'update:modelValue', val: string): void
}>()
const id = `date-picker-${new Date().getTime()}`
// 弹窗显隐
const visible = ref(false)
function togglePopup() {
  if (props.disabled) return
  visible.value = !visible.value
  if (visible.value) {
    // 弹出后测量位置
    nextTick(measurePosition)
    window.addEventListener('keydown', onKeydown)
  } else {
    window.removeEventListener('keydown', onKeydown)
  }
}
function closePopup() {
  visible.value = false
  window.removeEventListener('keydown', onKeydown)
}
function onKeydown(e: KeyboardEvent) {
  if (e.key === 'Escape') closePopup()
}
onBeforeUnmount(() => window.removeEventListener('keydown', onKeydown))

// 测量并设置下拉面板位置（uniapp 环境下用 createSelectorQuery）
const top = ref(0)
const left = ref(0)
const width = ref(0)
function measurePosition() {
  uni
    .createSelectorQuery()
    .select('#' + id)
    .boundingClientRect((rect: { bottom: number; left: number; width: number }) => {
      if (rect) {
        top.value = rect.bottom + 4
        left.value = rect.left
        width.value = 300
      }
    })
    .exec()
}

// 下拉样式
const calendarWidth = 320
const dropdownStyle = computed(() => ({
  position: 'absolute',
  top: `${top.value}px`,
  left:
    props.align === 'right'
      ? `${left.value + width.value - calendarWidth}px`
      : props.align === 'center'
        ? `${left.value + width.value / 2 - calendarWidth / 2}px`
        : `${left.value}px`,
  width: `${props.align === 'right' || props.align === 'center' ? calendarWidth : width.value}px`,
}))

// 当前年月
const current = ref(new Date())
const currentYear = computed(() => current.value.getFullYear())
const currentMonth = computed(() => current.value.getMonth())
function prevMonth() {
  const d = new Date(current.value)
  d.setMonth(d.getMonth() - 1)
  current.value = d
}
function nextMonth() {
  const d = new Date(current.value)
  d.setMonth(d.getMonth() + 1)
  current.value = d
}

// 星期几
const weekDays = ['一', '二', '三', '四', '五', '六', '日']
// 补零
function pad(n: number) {
  return String(n).padStart(2, '0')
}
// 今天 key
const today = new Date()
const todayStr = `${today.getFullYear()}-${pad(today.getMonth() + 1)}-${pad(today.getDate())}`

// 生成日期
const days = computed(() => {
  const y = current.value.getFullYear()
  const m = current.value.getMonth()
  const firstWeekDay = new Date(y, m, 1).getDay()
  const offset = (firstWeekDay + 6) % 7
  const count = new Date(y, m + 1, 0).getDate()
  const prevCount = new Date(y, m, 0).getDate()
  const total = 42
  const arr: any[] = []
  for (let i = 0; i < total; i++) {
    let date: Date
    let inCurrent = true
    if (i < offset) {
      date = new Date(y, m - 1, prevCount - offset + 1 + i)
      inCurrent = false
    } else if (i < offset + count) {
      date = new Date(y, m, i - offset + 1)
    } else {
      date = new Date(y, m + 1, i - offset - count + 1)
      inCurrent = false
    }
    const key = i
    const isToday =
      `${date.getFullYear()}-${pad(date.getMonth() + 1)}-${pad(date.getDate())}` === todayStr
    arr.push({ key, date, inCurrent, isToday })
  }
  return arr
})

// 双向绑定
const selected = ref(props.modelValue)
watch(
  () => props.modelValue,
  (v) => {
    console.log('props.modelValue,', props.modelValue)
    selected.value = v
  },
  { immediate: true },
)
const displayValue = computed(() => selected.value || props.placeholder || '请选择日期')

// 选中
function selectDate(day: any) {
  if (props.pickerOptions?.disabledDate && props.pickerOptions.disabledDate(day.date)) return
  const y = day.date.getFullYear()
  const m = pad(day.date.getMonth() + 1)
  const d = pad(day.date.getDate())
  const val = `${y}-${m}-${d}`
  selected.value = val
  emit('update:modelValue', val)
  closePopup()
}

// 是否选中
function isSelected(day: any) {
  const val = selected.value
  if (!val) return false
  const ds = `${day.date.getFullYear()}-${pad(day.date.getMonth() + 1)}-${pad(day.date.getDate())}`
  return ds === val
}

// 对齐 class
const alignClass = computed(() => `dp-align-${props.align || 'left'}`)

function clear() {
  selected.value = ''
  emit('update:modelValue', '')
}
</script>

<style lang="scss" scoped>
.date-picker-wrapper {
  position: relative;
  display: inline-block;
}
.dp-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 160px;
  height: 32px;
  border-radius: 32px;
  border: 1rpx solid #d9d9d9;
  background: #fff;
  cursor: pointer;
  padding: 0 16px;
}
.dp-input.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
.dp-text {
  font-size: 28rpx;
  color: #333;
  &.placeholder {
    color: #ccc;
  }
}
.dp-icon {
  font-size: 16rpx;
  color: #999;
}
.dp-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 998;
}
/* 下拉 */
.dp-dropdown {
  background: #fff;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  padding: 10rpx;
  border-radius: 8rpx;
  font-size: 14px;
  z-index: 999;
}
.dp-align-left {
}
.dp-align-center {
}
.dp-align-right {
}

.dp-cal-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10rpx;
}
.dp-nav {
  width: 40rpx;
  text-align: center;
  font-size: 32rpx;
}
.dp-month-label {
  font-size: 28rpx;
  font-weight: bold;
  margin: 0 20rpx;
}

.dp-weekdays {
  display: flex;
  margin-bottom: 10rpx;
  font-size: 14px;
}
.dp-weekday {
  flex: 1;
  text-align: center;
  color: #666;
}

.dp-days {
  display: flex;
  flex-wrap: wrap;
}
.dp-day {
  width: calc(100% / 7);
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  &:hover {
    color: $uv-primary;
  }
  &.disabled {
    color: #ccc;
    cursor: not-allowed;
    &:hover {
      color: #ccc;
    }
  }
}
.dp-day.other {
  color: #ccc;
}
.dp-day.today {
  background: rgba($uv-primary, 0.1);
  border-radius: 4rpx;
}
.dp-day.selected {
  background: $uv-primary;
  color: #fff;
  border-radius: 4rpx;
}
</style>
