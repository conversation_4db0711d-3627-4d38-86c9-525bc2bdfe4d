<template>
  <view class="video-wrap" :style="{ width, height }">
    <cover-view @tap="handleControls">
      <video
        class="video-player"
        :id="videoId"
        :style="{ width, height }"
        webkit-playsinline="true"
        playsinline="true"
        x-webkit-airplay="allow"
        x5-video-player-type="h5-page"
        x5-video-orientation="portrait"
        :src="src"
        :poster="poster"
        :initial-time="initialTime"
        :controls="isFullScreen"
        :show-center-play-btn="false"
        :autoplay="autoplay"
        :muted="isMute"
        @play="videoPlay"
        @pause="videoPause"
        @ended="videoEnded"
        @timeupdate="videoTimeUp"
        @loadeddata="videoLoaded"
        @loadedmetadata="videoLoaded"
        @seeked="videoSeeked"
        @seeking="videoSeeking"
        @waiting="videoWaiting"
        @error="videoError"
        @fullscreenchange="onFullScreen"
      ></video>
    </cover-view>

    <view v-if="isFullScreen" class="click-blocker" @touchmove.stop @click.stop></view>

    <!-- 中心播放按钮 -->
    <cover-view class="abs-center">
      <cover-image :src="playBtn" class="play-btn" v-if="!isVideoPlay" @tap="videoPlayCenter" />
      <!--      <cover-view class="video-loading" v-if="showLoading">-->
      <!--        <cover-image :src="loading" class="loading-btn" />-->
      <!--      </cover-view>-->
    </cover-view>

    <!-- 控制条 -->
    <cover-view :class="['controls-bar', controls ? 'show' : 'hide']">
      <cover-view class="play-box" @tap="videoPlayClick">
        <cover-image :src="pause" v-if="isVideoPlay" class="play-icon" @tap="videoPlayClick" />
        <cover-image :src="play" v-else class="play-icon" @tap="videoPlayClick" />
      </cover-view>
      <cover-view class="mute-box" @tap="videoMuteClick">
        <cover-image v-show="isMute" :src="mute" class="mute-icon" @tap="videoMuteClick" />
        <cover-image v-show="!isMute" :src="sound" class="mute-icon" @tap="videoMuteClick" />
      </cover-view>
      <cover-view class="progress">
        <!--        <cover-view class="currtime">{{ currentTimeStr }}</cover-view>-->
        <cover-view class="slider-container">
          <!--          <slider-->
          <!--            :step="step"-->
          <!--            :value="sliderValue"-->
          <!--            backgroundColor="#9f9587"-->
          <!--            activeColor="#d6d2cc"-->
          <!--            block-color="#FFFFFF"-->
          <!--            block-size="12"-->
          <!--            @change="sliderChange"-->
          <!--            @changing="sliderChanging"-->
          <!--          />-->
          <!-- 自定义进度条 -->
          <!--          <cover-view-->
          <!--            class="custom-slider"-->
          <!--            ref="sliderRef"-->
          <!--            @tap="onProgressTap"-->
          <!--            @touchstart="onTouchStart"-->
          <!--            @touchmove="onTouchMove"-->
          <!--            @touchend="onTouchEnd"-->
          <!--          >-->
          <!--            &lt;!&ndash; 背景轨道 &ndash;&gt;-->
          <!--            <cover-view class="slider-track">-->
          <!--              &lt;!&ndash; 播放进度条 &ndash;&gt;-->
          <!--              <cover-view class="slider-progress" :style="{ width: sliderValue + '%' }"></cover-view>-->
          <!--            </cover-view>-->

          <!--            &lt;!&ndash; 滑块 &ndash;&gt;-->
          <!--            <cover-view-->
          <!--              class="slider-thumb"-->
          <!--              :style="{ left: `calc(${sliderValue}% - 10rpx)` }"-->
          <!--            ></cover-view>-->
          <!--          </cover-view>-->
        </cover-view>
        <!--        <cover-view class="druationTime">{{ druationTimeStr }}</cover-view>-->
      </cover-view>

      <cover-view class="play-rate" @tap="videoPlayRate" v-if="showRate">
        {{ playbackRate }}x
      </cover-view>
      <cover-view class="play-full" @tap="videoFull">
        <cover-image :src="fullscreen" class="play-icon" @tap="videoFull" />
      </cover-view>

      <!-- 倍速菜单 -->
      <cover-view class="play-rate-menu" v-if="showRateMenu">
        <cover-view
          v-for="(item, index) in playbackRates"
          :key="index"
          :class="[{ activeRate: playbackRate === item }, 'play-rate-item']"
          @tap="changePlayRate(item)"
        >
          {{ item }}x
        </cover-view>
      </cover-view>
    </cover-view>
  </view>
</template>
<script setup lang="ts">
import loading from '@/static/video/loading.png'
import pause from '@/static/video/pause.png'
import play from '@/static/video/play.png'
import mute from '@/static/video/mute.png'
import sound from '@/static/video/sound.png'
import fullscreen from '@/static/video/fullscreen.png'
import playBtn from '@/static/video/play-btn.png'
const props = defineProps({
  videoId: { type: String, default: 'myVideo' },
  src: { type: String, required: true },
  autoplay: { type: Boolean, default: false },
  poster: { type: String, default: '' },
  step: { type: Number, default: 1 },
  progress: { type: Number, default: 0 },
  width: { type: String, default: '100%' },
  height: { type: String, default: '362px' },
  errorTip: { type: String, default: '播放错误' },
  showRate: { type: Boolean, default: true },
  playbackRates: { type: Array, default: () => [0.8, 1, 1.25, 1.5] },
})

const emit = defineEmits([
  'play',
  'pause',
  'ended',
  'error',
  'seeked',
  'seeking',
  'timeupdate',
  'loadeddata',
])
defineExpose({
  seek: (time: number) => {
    videoPlayer.seek(time)
  },
  pause: () => {
    videoPlayer.pause()
  },
})
const controls = ref(false)
const isVideoPlay = ref(false)
const isMute = ref(false)
const isVideoEnd = ref(false)
const showPoster = ref(true)
const showLoading = ref(false)
const durationTime = ref(0)
const currentTime = ref(0)
const druationTimeStr = ref('00:00')
const currentTimeStr = ref('00:00')
const sliderValue = ref(0)
const isSeeked = ref(true)
const playbackRate = ref(1)
const showRateMenu = ref(false)
const initialTime = ref(0)
const isFullScreen = ref(false)
const windowWidth = ref(uni.getSystemInfoSync().windowWidth)

let videoPlayer = null
let timer: number
let pauseTimer: number

const resumeTime = ref(0)

const formatSeconds = (s: number): string => {
  s = Math.round(s)
  const hh = Math.floor(s / 3600)
  const mm = Math.floor((s % 3600) / 60)
    .toString()
    .padStart(2, '0')
  const ss = Math.floor(s % 60)
    .toString()
    .padStart(2, '0')
  return (hh ? `${hh.toString().padStart(2, '0')}:` : '') + `${mm}:${ss}`
}

onMounted(() => {
  isVideoPlay.value = false // 强制控制初始为暂停状态
  videoPlayer = uni.createVideoContext(props.videoId)
  // #ifdef H5
  if (props.autoplay) {
    document.addEventListener('WeixinJSBridgeReady', () => {
      videoPlayer.play()
      isVideoPlay.value = true
    })
  }
  // #endif
})

onUnmounted(() => {
  clearTimeout(timer)
  clearTimeout(pauseTimer)
})

onHide(() => clearTimeout(timer))

const hideControls = () => {
  timer = setTimeout(() => (controls.value = false), 5000)
}

let lastTap = 0
const handleControls = () => {
  console.log('打开视频的控制条')
  const now = Date.now()
  if (now - lastTap < 300) return // 300ms 节流
  lastTap = now
  controls.value = !controls.value
}

const videoWaiting = () => {
  if (!isVideoPlay.value) showLoading.value = true
}

const videoLoaded = (e: any) => {
  durationTime.value = e.detail.duration
  druationTimeStr.value = formatSeconds(durationTime.value)
  console.log('props.progress * durationTime.value', props.progress, durationTime.value)
  initialTime.value = props.progress * durationTime.value
  sliderValue.value = props.progress * 100
  currentTimeStr.value = formatSeconds(initialTime.value)

  console.log('currentTimeStr', currentTimeStr.value)
  controls.value = false
  showLoading.value = false
  emit('loadeddata', durationTime.value)
}

const videoTimeUp = (e: any) => {
  const val = Math.round((e.detail.currentTime / durationTime.value) * 100)
  if (isSeeked.value && val % props.step === 0) sliderValue.value = val
  currentTimeStr.value = formatSeconds(e.detail.currentTime)
  // ✅ 记录当前播放进度
  resumeTime.value = e.detail.currentTime
  emit('timeupdate', e.detail.currentTime)
}

const sliderChanging = () => {
  isSeeked.value = false
  showLoading.value = true
  videoPlayer.pause()
  emit('seeking')
}

const sliderChange = (e: any) => {
  sliderValue.value = e.detail.value
  const ct = (sliderValue.value / 100) * durationTime.value
  videoPlayer.seek(ct)
  isSeeked.value = true
  showLoading.value = false
  if (sliderValue.value < 100) {
    videoPlayer.play()
  } else {
    videoPlayer.pause()
    videoEnded()
  }
  hideControls()
  emit('seeked', sliderValue.value)
}

const videoPlayCenter = () => {
  videoPlayer.play()
  emit('play')
}

const videoPlayClick = () => {
  if (isVideoPlay.value) {
    videoPlayer.pause()
  } else {
    videoPlayer.play()
    emit('play')
  }
}

const videoPlay = () => {
  clearTimeout(pauseTimer)
  isVideoPlay.value = true
  isVideoEnd.value = false
  showLoading.value = false
  hideControls()

  // ✅ 如果不是从头播放，手动 seek 到暂停位置
  if (resumeTime.value > 0) {
    setTimeout(() => {
      videoPlayer.seek(resumeTime.value)
    }, 300)
  }
}

const videoPause = () => {
  pauseTimer = setTimeout(() => {
    if (isVideoEnd.value || !isSeeked.value) return
    isVideoPlay.value = false
    emit('pause')
  }, 100)
}

const videoMuteClick = () => {
  isMute.value = !isMute.value
  if (!isMute.value && videoPlayer) {
    videoPlayer.volume(1.0) // ✅ 取消静音时手动设置最大音量
  }
}

const videoEnded = () => {
  isVideoPlay.value = false
  isVideoEnd.value = true
  showPoster.value = true
  resumeTime.value = 0
  emit('ended')
}

const videoError = () => {
  uni.showToast({ title: props.errorTip, icon: 'none' })
  emit('error')
}

const videoPlayRate = () => {
  showRateMenu.value = true
}

const changePlayRate = (rate: any) => {
  playbackRate.value = rate
  videoPlayer.playbackRate(rate)
  showRateMenu.value = false
  hideControls()
}

const videoFull = () => {
  videoPlayer.requestFullScreen()
  // #ifdef H5
  if (props.showRate) {
    // 添加倍速菜单等逻辑可继续优化
  }
  // #endif
}

const videoSeeking = () => {
  console.log('⏳ 视频正在拖动中（seeking）')
}

const videoSeeked = () => {
  console.log('✅ 拖动完成（seeked）')
}

const onFullScreen = ({ detail }: any) => {
  isFullScreen.value = detail.fullScreen
  console.log('sssssss', isFullScreen.value)
  if (!detail.fullScreen) {
    showRateMenu.value = false
  }
  // 全屏时隐藏导航栏
  if (isFullScreen.value) {
    hideNavigationBar()
  } else {
    showNavigationBar()
  }
}

// 隐藏导航栏
const hideNavigationBar = () => {
  // App 平台

  // #ifdef APP
  const webview = plus.webview.currentWebview()
  webview.setStyle({ titleNView: false })
  // #endif
}

// 显示导航栏
const showNavigationBar = () => {
  // App 平台
  // #ifdef APP
  const webview = plus.webview.currentWebview()
  webview.setStyle({
    titleNView: {
      autoBackButton: true, // 是否自动添加返回按钮
      titleText: '标题', // 你可以动态设置标题
    },
  })
  // #endif
}

const sliderRef = ref() // 注意：用于 selectorQuery 获取宽度

// 点击进度条跳转
const onProgressTap = (e: any) => {
  uni
    .createSelectorQuery()
    .in(uni)
    .select('.custom-slider')
    .boundingClientRect((rect) => {
      if (rect) {
        const tapX = e.detail.x || e.touches?.[0]?.clientX
        const percent = ((tapX - rect.left) / rect.width) * 100
        const clamped = Math.min(100, Math.max(0, percent))
        sliderValue.value = clamped
        seekTo(clamped)
      }
    })
    .exec()
}

// 拖拽控制
const dragging = ref(false)

const onTouchStart = () => {
  dragging.value = true
}

const onTouchMove = (e: any) => {
  uni
    .createSelectorQuery()
    .in(uni)
    .select('.custom-slider')
    .boundingClientRect((rect) => {
      if (rect) {
        const moveX = e.touches[0].clientX
        const percent = ((moveX - rect.left) / rect.width) * 100
        const clamped = Math.min(100, Math.max(0, percent))
        sliderValue.value = clamped
      }
    })
    .exec()
}

const onTouchEnd = () => {
  dragging.value = false
  seekTo(sliderValue.value)
}

const seekTo = (percent: number) => {
  const ct = (percent / 100) * durationTime.value
  videoPlayer.seek(ct)
  currentTimeStr.value = formatSeconds(ct)
  emit('seeked', percent)
}
</script>

<style lang="scss" scoped>
::v-deep .uni-video-fullscreen {
  margin-left: 30px;
}
.show {
  opacity: 1 !important;
}

.hide {
  opacity: 0 !important;
  pointer-events: none;
}

::v-deep .play-rate-item {
  white-space: normal !important;
  overflow: unset !important;
}
::v-deep uni-cover-view {
  white-space: normal !important; /* 允许换行 */
  overflow: visible !important; /* 取消裁剪，显示超出 */
  line-height: 1.5 !important; /* 调整行高更美观 */
}
.abs-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 100;
}

.video-wrap {
  position: relative;

  .play-btn {
    width: 120rpx;
    height: 120rpx;
  }

  @keyframes run {
    from {
      transform: rotate(0deg);
    }

    to {
      transform: rotate(360deg);
    }
  }

  .loading-btn {
    width: 120rpx;
    height: 120rpx;
    animation: run 0.8s linear 0s infinite;
  }

  .controls-bar {
    width: 100%;
    padding: 1% 1% 1% 0;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    align-items: center;
    background-color: rgba(59, 57, 57, 0.7);
    color: #fff;
    opacity: 1;
    transition: opacity 1s;
    height: 84rpx;

    .play-box,
    .mute-box,
    .play-full {
      width: 120rpx;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .mute-icon {
      width: 40rpx;
      height: 40rpx;
    }

    .play-icon {
      width: 34rpx;
      height: 34rpx;
    }

    .progress {
      display: flex;
      align-items: center;
      flex: 1;
      font-size: 24rpx;
      margin-left: 16rpx;

      .slider-container {
        flex: 1;
        max-width: 72%;
        z-index: 100;
      }

      .currtime {
        color: #ffffff;
        width: 11%;
        height: 100%;
        text-align: center;
        margin-right: 20rpx;
      }

      .druationTime {
        color: #ffffff;
        width: 12%;
        height: 100%;
        text-align: center;
      }
    }

    .play-rate-menu {
      height: 36px;
      position: absolute;
      right: 20px;
      top: 5px; // ✅ 往上移一点
      background-color: #000000;
      border-radius: 8rpx;
      padding: 16rpx;
      box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.3);
      z-index: 1000;
    }

    .play-rate-item {
      color: #fff;
      font-size: 28rpx;
      padding: 0rpx 20rpx;
      text-align: center;
      float: left;
      width: 50px;
    }

    .activeRate {
      color: #5785e3;
    }
  }
}

.custom-slider {
  position: relative;
  height: 30rpx;
  width: 100%;
}

.slider-track {
  width: 100%;
  height: 6rpx;
  background-color: #9f9587;
  border-radius: 3rpx;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}

.slider-progress {
  height: 100%;
  background-color: #d6d2cc;
  border-radius: 3rpx;
}

.slider-thumb {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background-color: #ffffff;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 2;
}
.click-blocker {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background: transparent;
}
</style>
