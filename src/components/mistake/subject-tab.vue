<template>
  <view class="cate-tab">
    <view
      v-for="(item, index) in list"
      :key="index"
      class="cate-item cursor-pointer"
      :class="{ active: activeId === item.subjectId }"
      @click="handleCateChange(item)"
    >
      {{ item.subjectName }}
    </view>
  </view>
</template>
<script setup lang="ts">
defineProps({
  list: {
    type: Array<ISubjectItem>,
    default: () => [],
  },
  activeId: {
    type: [String, Number],
    default: '',
  },
})
const emit = defineEmits(['change'])
const handleCateChange = (item) => {
  emit('change', item)
}
</script>
<style lang="scss" scoped></style>
