<template>
  <Card
    :title="isDeleteModel ? '选择收藏夹' : '收藏夹'"
    class-name="w-[530px]"
    content-class="flex-1 overflow-hidden relative flex flex-col"
  >
    <view class="flex-1 overflow-y-auto overflow-x-hidden flex flex-col gap-4">
      <NoData v-if="!list.length" />
      <view
        v-for="item in list"
        :key="item.foldeId"
        class="fav-item bg-white rounded-[6px] px-6 h-[72px] shrink-0 flex items-center justify-between cursor-pointer hover:text-primary"
        :class="{ active: activeFolder && activeFolder.foldeId === item.foldeId }"
        @click="handleItemClick(item)"
      >
        <text>{{ item.foldeName }}</text>
        <view
          v-if="isDeleteModel"
          class="flex items-center justify-center rounded-full size-[28px] border border-solid border-[#ddd]"
          :class="{
            'bg-white': !selectedItems.includes(item),
            'bg-primary': selectedItems.includes(item),
            'border-primary': selectedItems.includes(item),
          }"
        >
          <text class="iconfont icon-checked text-[16px] text-white"></text>
        </view>
      </view>
    </view>
    <view class="h-[56px] flex items-center justify-center gap-6 mt-[24px]">
      <template v-if="!isDeleteModel">
        <c-button size="large" shape="circle" @click="handleCreate">创建收藏夹</c-button>
        <c-button type="error" size="large" shape="circle" @click="handleDelete">
          删除收藏夹
        </c-button>
      </template>
      <template v-else>
        <c-button size="large" shape="circle" @click="handleCancelDelete">取消</c-button>
        <c-button
          :disabled="!selectedItems.length"
          type="error"
          size="large"
          shape="circle"
          @click="handleConfirmDelete"
        >
          确认删除
        </c-button>
      </template>
    </view>
  </Card>
  <FavoriteCreate ref="createModalRef" @success="handleCreateSuccess" />
</template>

<script lang="ts" setup>
import CButton from '@/components/common/button.vue'
import Card from '@/components/common/card.vue'
import FavoriteCreate from './favorite-create.vue'
import { useConfirm } from '@/hooks/useConfirm'
import { deleteFavoriteFold } from '@/service'
import NoData from '@/components/common/no-data.vue'

const { confirm } = useConfirm()

defineOptions({
  name: 'Favorite',
})
const props = defineProps({
  list: {
    type: Array<IFavoriteFoldItem>,
    default: () => [],
  },
})
watch(
  () => props.list,
  (val) => {
    if (val.length && !activeFolder.value) {
      activeFolder.value = val[0]
      emit('change', val[0])
    }
  },
  { deep: true, immediate: true },
)

const emit = defineEmits(['change', 'add', 'delete'])
const activeFolder = ref(null)
const createModalRef = ref(null)
const handleCreate = () => {
  createModalRef.value.open()
}
const handleCreateSuccess = () => {
  emit('add')
}

const isDeleteModel = ref(false)
const selectedItems = ref([])
const handleDelete = () => {
  console.log('删除收藏夹')
  isDeleteModel.value = true
}
const handleCancelDelete = () => {
  isDeleteModel.value = false
}
const handleConfirmDelete = () => {
  confirm({
    message: '确定要删除选中的文件夹吗？',
  }).then((res) => {
    if (res) {
      deleteFavoriteFold(selectedItems.value.map((i) => i.foldeId).join(',')).then(() => {
        uni.showToast({
          title: '删除成功',
          icon: 'none',
        })
        if (activeFolder.value && selectedItems.value.includes(activeFolder.value)) {
          activeFolder.value = null
        }
        isDeleteModel.value = false
        selectedItems.value = []
        emit('delete')
      })
    }
  })
}
const handleItemClick = (item) => {
  if (isDeleteModel.value) {
    if (selectedItems.value.includes(item)) {
      selectedItems.value = selectedItems.value.filter((i) => i.id !== item.id)
    } else {
      selectedItems.value.push(item)
    }
  } else {
    activeFolder.value = item
    emit('change', item)
  }
}
</script>

<style lang="scss" scoped>
.fav-item {
  &.active {
    background: #e5e5e5;
  }
}
</style>
