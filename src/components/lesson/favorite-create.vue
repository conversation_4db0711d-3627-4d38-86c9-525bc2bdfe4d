<template>
  <Modal
    title="创建收藏夹"
    ref="popupRef"
    confirm-text="确定"
    cancel-text="返回"
    @confirm="handleConfirm"
    @cancel="handleCancel"
    :loading="loading"
  >
    <uv-form
      labelPosition="left"
      :model="form"
      :rules="rules"
      ref="formRef"
      class="w-full"
      labelWidth="10px"
    >
      <uv-form-item prop="name">
        <uv-input
          v-model="form.name"
          placeholder="请输入文件夹名称"
          clearable
          shape="circle"
        ></uv-input>
      </uv-form-item>
    </uv-form>
  </Modal>
</template>

<script lang="ts" setup>
import Modal from '@/components/common/modal.vue'
import { addFavoriteFold } from '@/service'
defineOptions({
  name: 'CreateFavorite',
})

const popupRef = ref(null)
const open = () => {
  popupRef.value.open()
}
const close = () => {
  popupRef.value.close()
}
defineExpose({
  open,
  close,
})

const form = ref({
  name: '',
})

const loading = ref(false)
const formRef = ref(null)
const rules = {
  name: {
    type: 'string',
    required: true,
    message: '请填写名称',
    trigger: ['blur', 'change'],
  },
}
const emit = defineEmits(['success'])
const handleConfirm = async () => {
  try {
    await formRef.value.validate()
    console.log('验证通过')
    loading.value = true
    await addFavoriteFold({
      foldeName: form.value.name,
    })
    loading.value = false
    emit('success')
    close()
  } catch (error) {
    console.log('验证失败', error)
  }
}
const handleCancel = () => {
  close()
}
</script>

<style lang="scss" scoped></style>
