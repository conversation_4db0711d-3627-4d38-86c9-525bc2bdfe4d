<template>
  <view class="filter-blk">
    <view class="filter-label">{{ label }}</view>
    <view class="filter-list">
      <view class="flex flex-row items-center" v-if="type === 'time'">
        <DatePicker
          v-model="startDate"
          placeholder="开始日期"
          :pickerOptions="startPickerOptions"
        />
        <text class="text-black/80 text-[16px] mx-4">至</text>
        <DatePicker v-model="endDate" placeholder="结束日期" :pickerOptions="endPickerOptions" />
      </view>
      <view
        v-else
        class="filter-item"
        :class="{ active: active === item.id }"
        v-for="item in filterList"
        :key="item"
        @click="handleItemClick(item)"
      >
        {{ item.name }}
      </view>
    </view>
  </view>
</template>
<script setup lang="ts">
import DatePicker from '@/components/plan/date-picker.nvue'
import { ref, computed, watch } from 'vue'

export type IFilterItem = {
  name: string
  id: string
  versions?: IVersionItem[]
  teachers?: ITeacherItem[]
}
const props = defineProps({
  type: {
    type: String,
    default: 'list',
  },
  label: {
    type: String,
    default: '',
  },
  filterList: {
    type: Array<IFilterItem>,
    default: () => [],
  },
  active: {
    type: String,
    default: '',
  },
  startTime: {
    type: String,
    default: '',
  },
  endTime: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['change'])
const handleItemClick = (item) => {
  emit('change', item)
}
const startDate = ref('')
const endDate = ref('')

watch(
  [() => props.startTime, () => props.endTime],
  ([startTime, endTime]) => {
    if (startTime) {
      startDate.value = startTime.split(' ')[0]
    }
    if (endTime) {
      endDate.value = endTime.split(' ')[0]
    }
  },
  { deep: true, immediate: true },
)
const startPickerOptions = computed(() =>
  endDate.value
    ? {
        disabledDate(time) {
          return time.getTime() > new Date(endDate.value).getTime()
        },
      }
    : null,
)
const endPickerOptions = computed(() =>
  startDate.value
    ? {
        disabledDate(time) {
          return time.getTime() < new Date(startDate.value).getTime()
        },
      }
    : null,
)
watch([startDate, endDate], () => {
  emit('change', {
    startDate: startDate.value,
    endDate: endDate.value,
  })
})
</script>
<style lang="scss" scoped>
.filter-blk {
  flex-direction: row;
  gap: 16px;
  line-height: 32px;
  font-size: 20px;
  color: #999;
  .filter-label {
    white-space: nowrap;
    flex-shrink: 0;
    color: #333;
  }
  .filter-list {
    flex-direction: row;
    gap: 16px;
    .filter-item {
      cursor: pointer;
      padding: 0 16px;
      border-radius: 100px;
      &.active {
        background: #3483e9;
        color: #fff;
      }
    }
  }
}
</style>
