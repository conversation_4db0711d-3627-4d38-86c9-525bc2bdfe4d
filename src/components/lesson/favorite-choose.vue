<template>
  <Modal
    title="选择收藏夹"
    ref="popupRef"
    confirm-text="确定"
    cancel-text="创建收藏夹"
    @confirm="handleConfirm"
    @cancel="handleCreate"
    @showImage="updateValue"
  >
    <view class="max-h-[60vh] overflow-y-auto overflow-x-hidden flex flex-col gap-4">
      <NoData v-if="!favoriteList.length" text="暂无收藏夹" />
      <view
        v-for="item in favoriteList"
        :key="item.foldeId"
        class="fav-item bg-[#e5e5e5] rounded-[6px] px-6 h-[72px] shrink-0 flex items-center justify-between cursor-pointer hover:text-primary"
        @click="handleItemClick(item)"
      >
        <text>{{ item.foldeName }}</text>
        <view
          class="flex items-center justify-center rounded-full size-[28px] border border-solid border-[#D3D3D3]"
          :class="{
            'bg-white': !activeFav || activeFav.foldeId != item.foldeId,
            'bg-primary': activeFav && activeFav.foldeId === item.foldeId,
            'border-primary': activeFav && activeFav.foldeId === item.foldeId,
          }"
        >
          <text
            class="iconfont icon-checked text-[16px] text-white"
            :style="{ display: activeFav && activeFav.foldeId === item.foldeId ? 'block' : 'none' }"
          ></text>
        </view>
      </view>
    </view>
  </Modal>
  <FavoriteCreate ref="createModalRef" @success="handleCreateSuccess" />
</template>

<script lang="ts" setup>
import CButton from '@/components/common/button.vue'
import FavoriteCreate from './favorite-create.vue'
import NoData from '../common/no-data.vue'
import Modal from '../common/modal.vue'
import { getFavorite, addFavorite } from '@/service'
defineOptions({
  name: 'CreateFavorite',
})
const props = defineProps({
  show: { type: Boolean, default: false },
})
const emit = defineEmits(['success', 'update:value'])

const popupRef = ref(null)
const courseResourceId = ref('')
const open = (resourceId) => {
  courseResourceId.value = resourceId
  popupRef.value.open()
}
const close = () => {
  popupRef.value.close()
}
defineExpose({
  open,
  close,
})
const favoriteList = ref<IFavoriteFoldItem[]>([])
const activeFav = ref<IFavoriteFoldItem>(null)
const getFav = () => {
  getFavorite().then((res) => {
    favoriteList.value = res.data
  })
}
getFav()
const handleItemClick = (item) => {
  activeFav.value = item
}
const createModalRef = ref(null)
const handleCreate = () => {
  createModalRef.value.open()
}
const handleCreateSuccess = () => {
  getFav()
}

const handleConfirm = async () => {
  if (!activeFav.value) {
    await uni.showToast({
      title: '请选择收藏夹',
      icon: 'none',
    })
    return
  }
  try {
    await addFavorite({
      foldeId: activeFav.value.foldeId,
      courseResourceId: courseResourceId.value,
    })
    updateValue()
    emit('success', activeFav.value)
    close()
  } catch (error) {}
}

const updateValue = () => {
  emit('update:value', false)
}
</script>

<style lang="scss" scoped></style>
