<template>
  <view
    class="bg-white rounded-4 overflow-hidden relative cursor-pointer"
    @click="handleStudy(item)"
  >
    <view class="relative w-full pb-[70%]">
      <ImageView
        mode="aspectFill"
        :src="item.listImage"
        class="absolute top-0 left-0 size-full object-cover bg-white"
      />
      <view
        v-if="showTime"
        class="absolute bottom-0 left-0 w-full h-[36px] flex items-center px-5 text-lg text-white bg-black/50"
      >
        {{ item.viewTip }}
      </view>
    </view>
    <view class="p-4">
      <view class="font-bold text-xl mb-2 truncate leading-none">{{ item.courseName }}</view>
      <view class="text-lg text-black/60 truncate" v-if="showTime">{{ item.viewRemainTip }}</view>
      <view class="text-lg text-black/60 truncate" v-else>
        {{ item.subTitle }}
      </view>
    </view>
    <view
      class="absolute top-4 right-4 bg-black/40 flex items-center justify-center text-white text-xs rounded-1 h-5 px-1"
      v-if="item.isActivated == 1"
    >
      已开通
    </view>
  </view>
</template>
<script lang="ts" setup>
import ImageView from '../common/image-view.vue'
import { useConfirm } from '@/hooks/useConfirm'
const { confirm } = useConfirm()

defineProps({
  item: {
    type: Object,
    default: () => ({}),
  },
  showTime: {
    type: Boolean,
    default: false,
  },
})

const handleStudy = (item) => {
  if (item.isActivated !== 1) {
    confirm({
      message: '该课程暂未开通，请联系老师开通',
      showCancel: false,
    }).then((res) => {
      //
    })
    return
  }
  // :url="`/pages/lesson/detail?id=${item.courseId}`"
  uni.navigateTo({
    url: `/pages/lesson/detail?id=${item.courseId}`,
  })
}
</script>
<style lang="scss" scoped></style>
