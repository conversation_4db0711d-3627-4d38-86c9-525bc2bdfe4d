@import './iconfont.css';
.page {
  background-color: #e5e5e5;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  min-width: 1200px;
  color: #333;
  font-size: 16px;
}
page {
  background: #e5e5e5;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  min-width: 1200px;
  color: #333;
  font-size: 16px;

  // #ifndef APP-NVUE
  flex: 1;
  // #endif
  .uv-input {
    height: 56px !important;
    background-color: #fff;
    padding: 0 40rpx !important;
    .uni-input-input,
    .uni-input-placeholder {
      font-size: 36rpx !important;
    }
    .uv-input__content {
      gap: 8rpx;
    }
    &.uv-border {
      border-color: #ccc !important;
    }
  }
}

* {
  box-sizing: border-box;
}

.modal {
  @apply relative  flex flex-col  bg-white rounded-lg w-[532px];
  &-close {
    @apply absolute top-[14px] -right-[48px] cursor-pointer;
  }
  &-title {
    @apply flex items-center text-lg font-medium h-[60px] px-6;
    border-bottom: 1px solid #ebebeb;
  }
  &-content {
    @apply text-lg p-8;
  }
  &-footer {
    @apply pb-8 pt-0 px-8 flex items-center justify-center gap-6;
  }
  &.confirm-modal {
    @apply w-[400px];
    .modal-content {
      @apply text-center px-8 py-10;
    }
    .modal-title {
      @apply justify-center;
    }
  }
}
.filter-blk {
  @apply flex flex-row gap-4 leading-[32px] text-[20px] text-black/60;
  .filter-label {
    @apply whitespace-nowrap shrink-0 text-black/80;
  }
  .filter-list {
    @apply flex flex-wrap gap-4;
    .filter-item {
      @apply cursor-pointer px-4  rounded-full;
      &.active {
        @apply bg-primary text-white;
      }
    }
  }
}

.cate-tab {
  width: 100%;
  overflow-x: auto;
  overflow-y: hidden;
  display: flex;
  align-items: flex-end;
  gap: 40px;
  padding: 0 20px;
  font-size: 20px;
  position: relative;
  margin-bottom: 20px;
  color: #0009;
}
.cate-item {
  position: relative;
  line-height: 36px;
  white-space: nowrap;
  &:after {
    position: absolute;
    left: 50%;
    bottom: 0;
    transform: translateX(-50%);
    width: 70px;
    height: 12px;
    border-radius: 12px;
    background: rgba(#3483e9, 0.3);
  }
  &.active {
    font-weight: bold;
    color: #000;
    &:after {
      content: '';
    }
  }
  &:hover {
    color: #000;
  }
}

.mathjye-underline {
  text-decoration: underline;
}

.handout-content {
  height: 100%;
  overflow: hidden;
  padding: 20px;
  overflow: hidden;
}
