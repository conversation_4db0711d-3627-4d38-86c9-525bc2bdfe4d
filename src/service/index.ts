import { http } from '@/utils/http'
import type {
  ICourseItem,
  IClassTypeItem,
  IResourceItem,
  ISubjectItem,
  IMistakeItem,
  IPlanItem,
  IFavoriteFoldItem,
  IUserInfo,
} from '@/typings'

/** 登录 */
export type ILoginParams = {
  username: string
  password: string
  grantType: string
  tenantId: string
  clientId: string
  rememberMe: boolean
  macAddr: string
  ipAddr?: string
}
export const loginBox = (params: ILoginParams) => {
  return http.post<{ access_token: string; client_id: string; expire_in: number }>(
    '/auth/box/login',
    params,
  )
}
// 菁优登录
export const jyeooLogin = () => {
  return http.get<string>('/member/jyeoo/login')
}

// 获取用户信息
export const getProfile = () => {
  return http.get<IUserInfo>('/system/member/profile')
}
// 首页课程展示(大图、小图)
export const getHomeCourse = () => {
  return http.get<{
    top: ICourseItem
    others: ICourseItem[]
  }>('/member/homepage/course')
}
// 获取班型
export const getClassType = () => {
  return http.get<IClassTypeItem[]>('/member/classType')
}
// 获取课程
export const getCourse = (params: { typeId: number | string }) => {
  return http.get<{
    top: ICourseItem
    others: ICourseItem[]
  }>('/member/course', params)
}
// 获取课程详情
export const getCourseDetail = (params: { courseId: string }) => {
  return http.get<{ statistic: string; resource: IResourceItem[]; subjects: any[] }>(
    '/member/getSubjectVersionTeacher/byCourse',
    params,
  )
}
// 获取课程详情V2
export type ILessonDetailResourceItem = {
  unitId: string
  unitName: string
  resourceVoList: IResourceItem[]
}
export const getCourseDetailV2 = (params: { courseId: string }) => {
  try {
    return http.get<{
      statistic: string
      resource: ILessonDetailResourceItem[]
      subjects: any[]
    }>(`/member/getSubjectVersionTeacher/byCourseVTwo`, params)
  } catch (error) {
    console.log('error', error)
  }
}

// 获取我的课程
export const getMyCourse = () => {
  return http.get<{ total: string; course: ICourseItem[] }>('/member/myCourse')
}
// 获取科目
export const getSubject = () => {
  return http.get<ISubjectItem[]>('/member/courseSubject')
}
// 获取错题
export const getMistake = (params: { subjectId: number | string }) => {
  return http.get<IMistakeItem[]>('/member/mistake/note', params)
}
// 获取错题详情
export const getMistakeDetail = (params: { paperId: number | string }) => {
  return http.get('/jyeoo/jyeooPaper/queryAnalyzeByPaperId', params)
}
// 获取学习规划
export const getPlan = (params: { month: string }) => {
  return http.get<IPlanItem[][]>('/member/month/plan', params)
}
// 获取学习规划详情
export const getPlanDetail = (params: { startTime: string; endTime: string }) => {
  return http.get<{ statistic: string; resource: IResourceItem[]; subjects: any[] }>(
    '/member/getSubjectVersionTeacher/byTime',
    params,
  )
}
export const getPlanDetailV2 = (params: { startTime: string; endTime: string }) => {
  return http.get<{
    statistic: string
    resource: ILessonDetailResourceItem[]
    subjects: any[]
  }>('/member/getSubjectVersionTeacher/byTimeVTwo', params)
}

// 获取收藏
export const getFavorite = () => {
  return http.get<IFavoriteFoldItem[]>('/favorite/userFavoriteFolde/queryList')
}
// 收藏
export const addFavorite = (params: { foldeId: string; courseResourceId: string }) => {
  return http.post('/favorite/userFavoriteRecord', params)
}
// 创建收藏夹
export const addFavoriteFold = (params: { foldeName: string }) => {
  return http.post('/favorite/userFavoriteFolde', params)
}
// 删除收藏夹
export const deleteFavoriteFold = (foldeIds: string) => {
  return http.delete(`/favorite/userFavoriteFolde/${foldeIds}`)
}
// 删除收藏
export const deleteFavorite = (recordIds: string) => {
  return http.delete(`/favorite/userFavoriteRecord/${recordIds}`)
}
// 视频观看记录分秒
export const addStudyRecord = (params: {
  viewMinute: number
  viewSecond: number
  courseResourceId: string
}) => {
  return http.post('/studyPlan/studyRecord', params)
}

// 学习记录
export const getStudyRecord = () => {
  return http.get<{ date: string; course: ICourseItem[] }[]>(`/studyPlan/studyRecord/boxList`)
}
// 举一反三申请打印
export const applyPrintThink = (params: { resourceId: string; jyeooPaper: string }) => {
  return http.post('/student/apply/oneAndThree/print', params)
}
// 获取图片地址
export const getListOssByIds = (imgCode: string) => {
  return http.get(`/resource/oss/listByIds/${imgCode}`)
}

// 关于我们
export const getAbout = () => {
  return http.get<{ usContent: string }>(`/aboutUs/aboutUs/effective`)
}

// 修改密码
export const updatePwd = (params: { oldPassword: string; newPassword: string }) => {
  return http.put(`/member/updatePwd`, params)
}
// 获取组组卷
export const getPaper = (resourceId: string, paperType: string = 'practice') => {
  return http.get<{
    crpId: string
    paperContent: string
    resourceId: string
    subjectCode: string
    subjectId: string
  }>(`/system/courseResource/getPaper/${resourceId}?paperType=${paperType}`)
}
// 申请打印组组卷
export const printPaper = (params: {
  resourceId: string
  jyeooPaper: string
  paperType?: string
}) => {
  const finalParams = {
    ...params,
    paperType: params.paperType ?? 'practice',
  }
  return http.post(`/student/apply/resourcePaper/print`, finalParams)
}

// 申请打印讲义
export const printPdf = (params: { resourceId: string; pdfUrl: string }) => {
  return http.post(`/student/apply/printPdf`, params)
}

// 获取最新版本
export const queryVersion = () => {
  return http.get<{ boxAppVersion: string; boxAppUrl: string; isForceUpgrade: boolean }>(
    `/box/version/query`,
  )
}
