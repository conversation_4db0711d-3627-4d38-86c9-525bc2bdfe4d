<template>
  <view class="relative size-full py-[32px] pl-[80px] pr-[32px] flex flex-col overflow-hidden">
    <view class="back-btn" style="position: absolute" @click="handleBack">
      <text class="iconfont icon-back text-[20px]"></text>
    </view>
    <slot />
  </view>
  <ConfirmProvider />
</template>

<script lang="ts" setup>
import ConfirmProvider from '@/components/common/confirm-provider.vue'
const handleBack = () => {
  uni.navigateBack()
}
</script>
<style lang="scss" scoped>
.back-btn {
  @apply absolute top-0 left-0 size-[80px] flex items-center justify-center cursor-pointer;
}
</style>
