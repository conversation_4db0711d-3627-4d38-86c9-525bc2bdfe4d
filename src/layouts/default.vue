<!-- src/layouts/default.vue -->
<template>
  <view class="page flex">
    <view class="w-[240px] flex flex-col h-full p-10">
      <view class="flex flex-col items-center gap-3">
        <uv-image
          :src="userInfo?.avatar || '/static/images/default-avatar.png'"
          width="100px"
          height="100px"
          shape="circle"
        ></uv-image>
        <text class="text-[22px]">{{ userInfo?.nickName || userInfo?.userName || '--' }}</text>
      </view>
      <view class="flex-1 flex flex-col gap-2 mt-6 overflow-auto text-base">
        <view
          v-for="(item, index) in menuList"
          :key="index"
          class="menu-item"
          :class="{ active: item.url.indexOf(currentPath) > -1 }"
          @click="handleClick(item.url)"
        >
          <text :class="[item.icon, 'text-[22px]']"></text>
          <text class="whitespace-nowrap text-base">{{ item.name }}</text>
        </view>
      </view>
      <view class="flex flex-col items-center gap-1 text-base text-black/60">
        <text>{{ timeStr }}</text>
        <text>{{ weekStr }}</text>
        <text>{{ dateStr }}</text>
      </view>
      <view
        class="flex items-center justify-center text-base text-black/60 mt-4 cursor-pointer"
        @click="handleExit"
      >
        <text class="iconfont icon-exit"></text>
        <text>退出账号</text>
      </view>
    </view>
    <view class="flex-1 px-4 py-8 h-full overflow-hidden">
      <slot />
    </view>
  </view>
  <ConfirmProvider />
</template>

<script lang="ts" setup>
import ConfirmProvider from '@/components/common/confirm-provider.vue'
import { useConfirm } from '@/hooks/useConfirm'
import { useUserStore } from '@/store'
import { storeToRefs } from 'pinia'
const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)

const { confirm } = useConfirm()

const menuList = [
  {
    name: '首页',
    icon: 'iconfont icon-home',
    url: '/pages/home/<USER>',
  },
  {
    name: '课程',
    icon: 'iconfont icon-video',
    url: '/pages/lesson/index',
  },
  {
    name: '我的',
    icon: 'iconfont icon-user',
    url: '/pages/mine/index',
  },
  {
    name: '错题本',
    icon: 'iconfont icon-mistake',
    url: '/pages/mistake/index',
  },
  {
    name: '学习规划',
    icon: 'iconfont icon-plan',
    url: '/pages/plan/index',
  },
]
function getPagePath() {
  const pages = getCurrentPages()
  const current = pages[pages.length - 1]
  return current.route
}
const currentPath = getPagePath()

const handleClick = (url: string) => {
  uni.redirectTo({
    url,
  })
}

// 当前时间的 Date 对象
const now = ref(new Date())

// 格式化为 YYYY-MM-DD
const dateStr = computed(() => {
  const d = now.value
  const y = d.getFullYear()
  const m = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  return `${y}-${m}-${day}`
})

// 星期名称（中文）
const weekStr = computed(() => {
  const days = ['日', '一', '二', '三', '四', '五', '六']
  return `周${days[now.value.getDay()]}`
})

// 格式化为 HH:mm:ss
const timeStr = computed(() => {
  const d = now.value
  const hh = String(d.getHours()).padStart(2, '0')
  const mm = String(d.getMinutes()).padStart(2, '0')
  const ss = String(d.getSeconds()).padStart(2, '0')
  return `${hh}:${mm}:${ss}`
})

let timer: number
onMounted(() => {
  // 每秒更新一次 now
  timer = setInterval(() => {
    now.value = new Date()
  }, 1000)
  const info = uni.getSystemInfoSync()
  const screenWidth = info.screenWidth // 设备屏幕的宽度（px）
  const screenHeight = info.screenHeight // 设备屏幕的高度（px）
  console.log('屏幕宽度：', screenWidth, '屏幕高度：', screenHeight)
})
onUnmounted(() => {
  clearInterval(timer)
})

const handleExit = () => {
  confirm({
    message: '确定要退出吗？',
  }).then((res) => {
    if (res) {
      // uni.clearStorageSync()
      userStore.logout()
      // uni.reLaunch({
      //   url: '/pages/login/index',
      // })
    }
  })
}
</script>
<style lang="scss" scoped>
.menu-item {
  height: 56px;
  white-space: nowrap;
  @apply flex items-center gap-3 px-6 rounded-full cursor-pointer shrink-0;
  &:hover {
    color: $uv-primary;
  }
  &.active {
    background: $uv-primary;
    color: #fff;
  }
}
</style>
