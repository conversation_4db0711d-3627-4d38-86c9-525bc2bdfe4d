/* eslint-disable camelcase */
import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import { getProfile, ILoginParams, loginBox, jyeooLogin } from '@/service'
import type { IAuthInfo, IUserInfo } from '@/typings'

export const useUserStore = defineStore(
  'user',
  () => {
    const initState = uni.getStorageSync('userInfo')
    const userInfo = ref<IUserInfo>({ ...initState })
    const getUserInfo = async () => {
      const res = await getProfile()
      setUserInfo(res.data)
    }
    const setUserInfo = (val: IUserInfo) => {
      userInfo.value = val
    }

    const clearUserInfo = () => {
      userInfo.value = null
    }

    const authInfo = ref<IAuthInfo>(null)
    const setAuthInfo = (val: IAuthInfo) => {
      authInfo.value = val
    }
    const clearAuthInfo = () => {
      authInfo.value = null
    }
    const jyeooAuthInfo = ref<{ token: string }>(null)
    const setJyeooAuthInfo = (val: string) => {
      jyeooAuthInfo.value = { token: val }
    }
    const clearJyeooAuthInfo = () => {
      jyeooAuthInfo.value = null
    }

    const mac = ref('')
    const setMac = (val: string) => {
      mac.value = val
    }
    const clearMac = () => {
      mac.value = ''
    }
    const logout = () => {
      clearUserInfo()
      clearAuthInfo()
      clearJyeooAuthInfo()
      clearMac()
      uni.reLaunch({
        url: '/pages/login/index',
      })
    }
    const login = (params: ILoginParams) => {
      return new Promise((resolve, reject) => {
        loginBox(params)
          .then((res) => {
            const { access_token, client_id, expire_in } = res.data
            setAuthInfo({
              token: access_token,
              clientId: client_id,
              expireTime: new Date().getTime() + expire_in * 1000,
            })
            getUserInfo()
            jyeooLogin()
              .then((jyeooRes) => {
                setJyeooAuthInfo(jyeooRes.msg)
                resolve(res)
              })
              .catch((err) => {
                console.log('jyeooLogin err: ', err)
                resolve(res)
              })
          })
          .catch((err) => {
            reject(err)
          })
      })
    }
    const isLogin = computed(() => {
      return (
        authInfo.value &&
        authInfo.value.expireTime &&
        authInfo.value.expireTime > new Date().getTime()
      )
    })

    return {
      mac,
      setMac,
      userInfo,
      setUserInfo,
      clearUserInfo,
      getUserInfo,
      authInfo,
      setAuthInfo,
      clearAuthInfo,
      jyeooAuthInfo,
      setJyeooAuthInfo,
      clearJyeooAuthInfo,
      logout,
      login,
      isLogin,
    }
  },
  {
    persist: true,
  },
)
