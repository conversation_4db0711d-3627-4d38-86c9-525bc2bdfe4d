// src/hooks/useConfirm.js
import { reactive, ref } from 'vue'

/** 全局可见性和配置状态 */
const visible = ref(false)
const state = reactive({
  title: '提示',
  message: '',
  confirmText: '确定',
  cancelText: '取消',
  showCancel: true,
})

let resolveFn = null

/**
 * 供业务组件调用的 confirm 接口
 * @param {Object} options
 * @param {string} options.title
 * @param {string} options.message
 * @param {string} options.confirmText
 * @param {string} options.cancelText
 * @param {boolean} options.showCancel
 * @returns {Promise<boolean>}
 */
export function useConfirm() {
  function confirm(options) {
    state.title = options.title || state.title
    state.message = options.message || state.message
    state.confirmText = options.confirmText || state.confirmText
    state.cancelText = options.cancelText || state.cancelText
    state.showCancel =
      typeof options.showCancel === 'boolean' ? options.showCancel : state.showCancel
    visible.value = true
    console.log('confirm', visible.value)
    return new Promise((resolve) => {
      resolveFn = resolve
    })
  }
  return { confirm }
}

/**
 * 供 Provider 组件渲染和回调使用
 */
export function useConfirmState() {
  function handleConfirm() {
    visible.value = false
    resolveFn?.(true)
  }
  function handleCancel() {
    visible.value = false
    resolveFn?.(false)
  }
  return {
    visible,
    state,
    handleConfirm,
    handleCancel,
  }
}
