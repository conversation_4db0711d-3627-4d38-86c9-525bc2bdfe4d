/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by vite-plugin-uni-pages

interface NavigateToOptions {
  url: "/pages/home/<USER>" |
       "/pages/home/<USER>" |
       "/pages/lesson/detail" |
       "/pages/lesson/exam" |
       "/pages/lesson/handout" |
       "/pages/lesson/handout1" |
       "/pages/lesson/index" |
       "/pages/login/index" |
       "/pages/mine/account" |
       "/pages/mine/collect" |
       "/pages/mine/history" |
       "/pages/mine/index" |
       "/pages/mine/password" |
       "/pages/mistake/detail" |
       "/pages/mistake/index" |
       "/pages/mistake/think" |
       "/pages/plan/index";
}
interface RedirectToOptions extends NavigateToOptions {}

interface SwitchTabOptions {
  
}

type ReLaunchOptions = NavigateToOptions | SwitchTabOptions;

declare interface Uni {
  navigateTo(options: UniNamespace.NavigateToOptions & NavigateToOptions): void;
  redirectTo(options: UniNamespace.RedirectToOptions & RedirectToOptions): void;
  switchTab(options: UniNamespace.SwitchTabOptions & SwitchTabOptions): void;
  reLaunch(options: UniNamespace.ReLaunchOptions & ReLaunchOptions): void;
}
