<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="8dp"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="visible">

            <ImageView
                android:id="@+id/loading_animation"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:scaleType="centerInside"
                android:src="@mipmap/ic_loading_sany" />

            <TextView
                android:id="@+id/loading_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="15dp"
                android:text="努力加载中&#8230;"
                android:textColor="#ffffffff"
                android:textSize="14sp" />

        </LinearLayout>
    </RelativeLayout>

</RelativeLayout>