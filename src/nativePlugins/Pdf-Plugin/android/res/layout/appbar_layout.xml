<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.appbar.AppBarLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/tv_title"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?attr/colorPrimary"
    android:minHeight="?attr/actionBarSize"
    android:theme="@style/ThemeOverlay.AppCompat.DayNight.ActionBar.Black"
    app:elevation="0dp">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minHeight="?attr/actionBarSize"
        app:elevation="0dp">

        <TextView
            android:id="@+id/tv_toolbar_title"
            android:layout_width="220dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:singleLine="true"
            android:text="登录"
            android:textAlignment="center"
            android:textColor="@color/black"
            android:textSize="18sp" />
    </androidx.appcompat.widget.Toolbar>
</com.google.android.material.appbar.AppBarLayout>
