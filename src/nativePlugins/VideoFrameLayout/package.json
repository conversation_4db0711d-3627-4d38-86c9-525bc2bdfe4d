{"name": "video_frameLayout", "id": "VideoFrameLayout", "version": "1.0.2", "description": "VideoFrameLayout", "_dp_type": "nativeplugin", "_dp_nativeplugin": {"android": {"plugins": [{"type": "component", "name": "VideoFrameLayout", "class": "com.sevendu.tv_video.VideoFrameLayout"}], "integrateType": "aar", "dependencies": ["androidx.appcompat:appcompat:1.7.0", "com.h6ah4i.android.widget.verticalseekbar:verticalseekbar:1.0.0", "androidx.activity:activity:1.10.1", "androidx.constraintlayout:constraintlayout:2.2.1", "androidx.media3:media3-exoplayer:1.5.1", "androidx.media3:media3-ui:1.5.1", "com.github.bumptech.glide:glide:4.16.0"], "permissions": [], "minSdkVersion": "26"}}}