<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5">
{
  layout: 'webview',
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '讲义',
  },
  type: 'nvue',
}
</route>
<template>
  <view class="webview-layout">
    <view class="back-btn" @click="handleBack">
      <image src="/static/images/back.png" style="width: 20px; height: 20px" />
    </view>
    <!-- <text>212121</text> -->
    <view class="card">
      <web-view
        :src="`${localViewer}?file=${encodeURIComponent(pdfUrl)}`"
        class="pdf-webview"
        v-if="pdfUrl"
      />
      <!-- <web-view
        style="width: 800px; height: 300px"
        :src="pdfUrl"
        class="pdf-webview"
        v-if="pdfUrl"
      /> -->
      <!-- <PdfViewer :url="pdfUrl" v-if="pdfUrl" /> -->
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
defineOptions({
  name: 'Lesson',
})

const handleBack = () => {
  uni.navigateBack()
}
const localViewer = plus.io.convertLocalFileSystemURL('_www/static/pdfjs/web/viewer.html')

const pdfUrl = ref<string>('')
onLoad((options) => {
  console.log('onLoad')
  pdfUrl.value = options.pdf
})
</script>

<style lang="scss" scoped>
.webview-layout {
  position: fixed;
  top: 0;
  left: 0;
  width: 1920px;
  height: 800px;
  padding: 32px 32px 32px 80px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  flex: 1;
  background: #f5f5f5;
}
.back-btn {
  position: absolute;
  top: 0;
  left: 0;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.card {
  flex: 1;
  overflow: hidden;
  background: #fff;
  border-radius: 14px;
  padding: 20px;
  border-radius: 14px;
}
.pdf-webview {
  flex: 1;
  background: #0ff;
}
</style>
