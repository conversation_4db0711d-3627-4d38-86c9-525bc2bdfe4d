<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5">
{
  layout: 'inner',
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '课程详情',
  },
}
</route>
<template>
  <view class="h-full flex items-stretch gap-6 relative">
    <view class="w-[639px] shrink-0 bg-white overflow-hidden rounded-[14px]">
      <view class="relative w-full h-[360px]">
        <uv-image :src="poster" v-if="showImage" width="639px" height="362px" />
        <x-video
          v-show="!showImage"
          v-if="activeResource && showVideo"
          ref="videoPlayer"
          class="size-full video"
          video-id="myVideo"
          :src="activeResource.videoCourseLink"
          :key="activeResource.videoCourseLink"
          :poster="poster"
          :progress="progress"
          @play="videoPlay"
          @pause="videoPause"
          @ended="videoEnded"
          @timeupdate="videoTimeUp"
          @loadeddata="videoLoaded"
          @seeking="videoSeeking"
          @seeked="videoSeeked"
          @error="videoError"
        />
        <!-- 倍速按钮 -->
      </view>

      <view class="p-5 relative" v-if="activeResource">
        <view class="flex items-center gap-4">
          <view class="text-[28px] font-bold">{{ activeResource.videoResourceName }}</view>
          <text
            class="iconfont icon-fav-fill text-[28px] text-[#ccc] cursor-pointer"
            v-if="activeResource.collectStatus != 1"
            @click="handleFav"
          ></text>
          <text
            class="iconfont icon-fav-fill text-[28px] text-[#FD9F4D] cursor-pointer"
            v-else
          ></text>
        </view>
        <view
          class="flex items-center gap-4 mt-10 text-[20px] cursor-pointer"
          v-if="activeResource.courseNotes != '' && activeResource.courseNotes != undefined"
        >
          <view class="text-black/60">配套资料：</view>
          <!-- #ifdef APP -->
          <view @click="previewPdf" class="flex items-center gap-2 text-primary" hover-class="none">
            <text class="iconfont icon-search text-[20px]"></text>
            <text>讲义</text>
          </view>
          <c-button type="primary" @click="handlePrint" size="small">申请打印</c-button>
          <!-- #endif -->
          <!-- #ifdef H5 -->
          <navigator
            :url="`/pages/lesson/handout?id=${activeResource.resourceId}&pdf=${activeResource.courseNotes}`"
            class="flex items-center gap-2 text-primary"
            hover-class="none"
          >
            <text class="iconfont icon-search text-[20px]"></text>
            <text>讲义</text>
          </navigator>
          <!-- #endif -->
        </view>
        <view
          class="flex items-center gap-4 mt-6 text-[20px]"
          v-if="activeResource.jyeooPaperIds != '' && activeResource.jyeooPaperIds != undefined"
        >
          <view class="text-black/60">课后练习题：</view>
          <navigator
            :url="`/pages/lesson/exam?id=${activeResource.resourceId}&name=${activeResource.videoResourceName}&subjectCode=${activeResource.subjectCode}&pointNo=${activeResource.pointNo}&paperType=practice`"
            class="flex items-center gap-2 text-primary"
            hover-class="none"
          >
            <text class="iconfont icon-note text-[20px]"></text>
            <text>练习题</text>
          </navigator>
        </view>

        <view
          class="flex items-center gap-4 mt-6 text-[20px]"
          v-if="
            activeResource.jyeooTestPaperIds != '' && activeResource.jyeooTestPaperIds != undefined
          "
        >
          <view class="text-black/60">课后测试题：</view>
          <navigator
            :url="`/pages/lesson/exam?id=${activeResource.resourceId}&name=${activeResource.videoResourceName}&subjectCode=${activeResource.subjectCode}&pointNo=${activeResource.pointNo}&paperType=test`"
            class="flex items-center gap-2 text-primary"
            hover-class="none"
          >
            <text class="iconfont icon-note text-[20px]"></text>
            <text>测试题</text>
          </navigator>
        </view>
      </view>
    </view>
    <view class="flex-1 flex flex-col gap-6 w-[30%]">
      <Card content-class="flex flex-col gap-5">
        <FilterBlk
          label="科目"
          :filter-list="subjectList"
          :active="activeSubject"
          @change="handleSubjectChange"
        />
        <FilterBlk
          label="版本"
          :filter-list="versionList"
          :active="activeVersion"
          v-if="versionList.length"
          @change="handleVersionChange"
        />
        <FilterBlk
          label="名师"
          :filter-list="teacherList"
          :active="activeTeacher"
          v-if="teacherList.length"
          @change="handleTeacherChange"
        />
        <FilterBlk
          v-if="options.type === 'plan'"
          label="日期"
          type="time"
          :startTime="options.startDate"
          :endTime="options.endDate"
          @change="handleDateChange"
        />
      </Card>
      <Card :subtitle="statistic" class-name="flex-1" content-class="flex-1 overflow-y-auto">
        <view class="flex flex-col gap-6">
          <template v-for="unitItem in dataList" :key="unitItem.unitId">
            <view
              class="rounded-[6px] px-6 h-[72px] shrink-0 flex gap-4 items-center justify-between cursor-pointer"
              style="background: #e5e5e5"
              @click="handleUnitClick(unitItem)"
            >
              <text
                class="iconfont icon-tri text-[#BFBFBF] text-[20px] transition-all"
                :style="{ transform: activeUnitId === unitItem.unitId ? `rotate(90deg)` : '' }"
              ></text>
              <view class="flex-1 text-black/60 text-xl truncate font-bold">
                {{ unitItem.unitName }}
              </view>
              <text class="text-black/60">({{ unitItem.resourceVoList.length }}讲)</text>
            </view>
            <view class="flex flex-col gap-3 -mt-3" v-if="activeUnitId === unitItem.unitId">
              <view
                v-for="(item, index) in unitItem.resourceVoList"
                :key="index"
                class="rounded-[6px] pl-12 pr-6 h-[60px] shrink-0 flex gap-4 items-center justify-between"
                style="background: #e5e5e5"
              >
                <text
                  class="iconfont icon-stat text-primary text-[20px]"
                  v-if="activeResource && activeResource.resourceId === item.resourceId"
                ></text>
                <view
                  class="flex-1 cursor-pointer text-black/60 hover:text-primary text-xl truncate"
                  @click="handleItemClick(item)"
                >
                  {{ item.videoResourceName }}
                </view>
                <view class="text-black/60" v-if="item.viewMinute > 0 || item.viewSecond > 0">
                  已学习{{ item.viewMinute || '0' }}分{{ item.viewSecond || '0' }}秒
                </view>
              </view>
            </view>
          </template>
        </view>
      </Card>
    </view>
  </view>
  <FavoriteChoose
    ref="chooseModalRef"
    :show="showImage"
    @success="handleFavSuccess"
    @update:value="handleStatus"
  />
  <Loading v-if="loading" />
</template>

<script lang="ts" setup>
import { nextTick, onBeforeUnmount } from 'vue'
import Loading from '@/components/common/full-loading.vue'
import Card from '@/components/common/card.vue'
import FavoriteChoose from '@/components/lesson/favorite-choose.vue'
import FilterBlk, { IFilterItem } from '@/components/lesson/filter-blk.vue'
import CButton from '@/components/common/button.vue'
import XVideo from '@/components/x-video/x-video.vue'
import {
  ILessonDetailResourceItem,
  getCourseDetailV2,
  getPlanDetailV2,
  addStudyRecord,
  printPdf,
  getListOssByIds,
} from '@/service'

const showImage = ref(false)

// #ifdef APP
const plugin = uni.requireNativePlugin('Pdf-Plugin')

const previewPdf = () => {
  plugin.showPdf({
    title: activeResource.value.videoResourceName,
    url: activeResource.value.courseNotes,
    maxScale: '20',
    waterMark: '聪学伴学',
  })
}
// #endif

const handlePrint = async () => {
  try {
    await printPdf({
      resourceId: activeResource.value.resourceId,
      pdfUrl: activeResource.value.courseNotes,
    })
    await uni.showToast({
      title: '申请成功',
    })
  } catch (error) {
    console.log('error', error)
  }
}

defineOptions({
  name: 'LessonDetail',
})

const loading = ref(false)
const options = ref<Record<string, any>>({})
const allResource = ref<ILessonDetailResourceItem[]>([])
const allSubjects = ref<ISubjectItem[]>([])
const statistic = ref<string>('')
const activeSubject = ref<string>('all')
const activeVersion = ref<string>('all')
const activeTeacher = ref<string>('all')
const activeResource = ref<IResourceItem>(null)

// 视频播放组件
const videoPlayer = ref()
const progress = ref(0)
const currentTime = <any>ref(0)
const poster = ref('')

watch(
  () => activeResource.value?.detailImage,
  async (val) => {
    if (val) {
      const res = await getListOssByIds(val)
      if (res.data && res.data[0]) {
        poster.value = res.data[0].url
      }
    } else {
      poster.value = ''
    }
  },
  { deep: true, immediate: true },
)
const subjectList = computed<IFilterItem[]>(() => {
  return [
    {
      id: 'all',
      name: '全部',
    },
    ...allSubjects.value.map((item) => ({
      id: item.subjectName,
      name: item.subjectName,
      versions: item.versions,
    })),
  ]
})
const versionList = computed<IFilterItem[]>(() => {
  if (!subjectList.value.length || activeSubject.value === 'all') {
    return []
  }
  const tempList = allSubjects.value
    .find((item) => item.subjectName === activeSubject.value)
    .versions.map((item) => ({
      id: item.versionName,
      name: item.versionName,
      teachers: item.teachers,
    }))
  return [
    {
      id: 'all',
      name: '全部',
    },
    ...tempList,
  ]
})
const teacherList = computed<IFilterItem[]>(() => {
  if (!versionList.value.length || activeVersion.value === 'all') return []
  const tempList = versionList.value
    .find((item) => item.id === activeVersion.value)
    .teachers.map((item) => ({
      id: item.teacherName,
      name: item.teacherName,
    }))
  return [
    {
      id: 'all',
      name: '全部',
    },
    ...tempList,
  ]
})
const dataList = computed<ILessonDetailResourceItem[]>(() => {
  if (activeSubject.value === 'all') {
    return [...allResource.value]
  }
  if (activeVersion.value === 'all') {
    const temp = []
    allResource.value.forEach((item) => {
      const subjectList = item.resourceVoList.filter(
        (item) => item.subjectName === activeSubject.value,
      )
      if (subjectList.length > 0) {
        temp.push({
          ...item,
          resourceVoList: subjectList,
        })
      }
    })
    return temp
  }
  if (activeTeacher.value === 'all') {
    const temp = []
    allResource.value.forEach((item) => {
      const subjectList = item.resourceVoList.filter(
        (item) =>
          item.subjectName === activeSubject.value && item.versionName === activeVersion.value,
      )
      if (subjectList.length > 0) {
        temp.push({
          ...item,
          resourceVoList: subjectList,
        })
      }
    })
    return temp
  }
  const temp = []
  allResource.value.forEach((item) => {
    const subjectList = item.resourceVoList.filter(
      (item) =>
        item.subjectName === activeSubject.value &&
        item.versionName === activeVersion.value &&
        item.teacherName === activeTeacher.value,
    )
    if (subjectList.length > 0) {
      temp.push({
        ...item,
        resourceVoList: subjectList,
      })
    }
  })
  return temp
})

watch(
  () => activeSubject.value,
  (val) => {
    activeVersion.value = 'all'
    activeTeacher.value = 'all'
  },
)
watch(
  () => activeVersion.value,
  (val) => {
    activeTeacher.value = 'all'
  },
)

onLoad((opts) => {
  options.value = opts
  if (opts.type === 'plan') {
    uni.setNavigationBarTitle({
      title: '学习计划详情',
    })
  } else if (opts.id) {
    uni.setNavigationBarTitle({
      title: '课程详情',
    })
  }
  initData()
})
// 初始数据
const initData = () => {
  if (options.value.type === 'plan') {
    // 课程查新
    getPlanDetailData(options.value)
  } else if (options.value.id) {
    // 所有视频查询
    getDetail(options.value.id)
  }
}
// 所有视频查询
const getDetail = async (courseId) => {
  try {
    loading.value = true
    const { data } = await getCourseDetailV2({
      courseId,
    })
    loading.value = false
    allResource.value = data.resource.map((item) => ({
      ...item,
      open: false,
    }))
    allSubjects.value = data.subjects
    statistic.value = data.statistic
  } catch (error) {
    loading.value = false
    console.log('error', error)
  }
}
// 排课查询视频
const getPlanDetailData = async (options) => {
  try {
    loading.value = true
    const { data } = await getPlanDetailV2({
      startTime: options.startDate,
      endTime: options.endDate,
    })
    loading.value = false
    allResource.value = data.resource
    allSubjects.value = data.subjects
    statistic.value = data.statistic
  } catch (error) {
    loading.value = false
    console.log('error', error)
  }
}
const activeUnitId = ref<string>('')
const handleUnitClick = (item) => {
  if (activeUnitId.value === item.unitId) {
    activeUnitId.value = ''
    return
  }
  activeUnitId.value = item.unitId
}

const formatTime = (sec: number) => {
  const m = Math.floor(sec / 60)
  const s = Math.floor(sec % 60)
  return { minutes: m, seconds: s }
}

const updateTotalStudyTime = () => {
  const lessonList = dataList.value.map((item) => item.resourceVoList).flat()
  const studyLesson = lessonList.filter((item) => item.viewMinute > 0 || item.viewSecond > 0)
  statistic.value = `已学习${studyLesson.length}节，共${lessonList.length}节`
}
const updateStudyRecord = async (activeResource, viewMinute, viewSecond) => {
  const viewTotal = viewMinute * 60 + viewSecond
  const currentM = activeResource.viewMinute || 0
  const currentS = activeResource.viewSecond || 0
  const currentTotal = currentM * 60 + currentS
  if (viewTotal <= currentTotal) return

  await addStudyRecord({
    courseResourceId: activeResource.resourceId,
    viewMinute,
    viewSecond,
  })
  nextTick(() => {
    updateTotalStudyTime()
  })
}

const chooseModalRef = ref(null)
const handleFav = () => {
  showImage.value = true
  chooseModalRef.value.open(activeResource.value.resourceId)
  videoPlayer.value.pause()
}
const handleFavSuccess = (item) => {
  activeResource.value.collectStatus = 1
}

/**
 * 修改展示
 */
const handleStatus = () => {
  showImage.value = false
}

const handleSubjectChange = (item) => {
  activeSubject.value = item.id
}
const handleVersionChange = (item) => {
  activeVersion.value = item.id
}
const handleTeacherChange = (item) => {
  activeTeacher.value = item.id
}

const handleItemClick = (item: any) => {
  console.log('handleItemClick', item.viewMinute, item.viewSecond)
  const temp = formatTime(currentTime.value)
  // 播放到指定的位置
  currentTime.value = item.viewMinute * 60 + item.viewSecond
  // 保存上次看的数据
  updateStudyRecord(activeResource.value, temp.minutes, temp.seconds)
  activeResource.value.viewMinute = temp.minutes
  activeResource.value.viewSecond = temp.seconds
  // 下一个数据
  activeResource.value = item
}
watch(
  () => dataList.value,
  (val) => {
    if (val.length > 0 && !activeResource.value) {
      if (options.value.resourceId) {
        const temp = val.find((item) =>
          item.resourceVoList.find((item) => item.resourceId === options.value.resourceId),
        )
        activeUnitId.value = temp.unitId
        activeResource.value = temp.resourceVoList.find(
          (item) => item.resourceId === options.value.resourceId,
        )
        // 播放到指定的位置
        currentTime.value = activeResource.value.viewMinute * 60 + activeResource.value.viewSecond
      } else {
        activeUnitId.value = val[0].unitId
        activeResource.value = val[0].resourceVoList[0]
        // 播放到指定的位置
        currentTime.value = activeResource.value.viewMinute * 60 + activeResource.value.viewSecond
      }
    }
  },
  { deep: true, immediate: true },
)
const handleDateChange = (value) => {
  console.log('handleDateChange', value)
  initData()
}

const showVideo = ref(true)
onBeforeUnmount(() => {
  console.log('onBeforeUnmount')
  const temp = formatTime(currentTime.value)
  const viewMinute = temp.minutes
  const viewSecond = temp.seconds
  updateStudyRecord(activeResource.value, viewMinute, viewSecond)
  showVideo.value = false
  videoPlayer.value?.pause() // 👉 暂停视频
})

// 视频信息加载完成
const videoLoaded = (durationTime: number) => {
  console.log('视频总时长:', durationTime)
}

// 当前播放时间
const videoTimeUp = (e: number) => {
  console.log('当前时间:', e)
  currentTime.value = e
}

// 点击原始播放
const videoPlay = () => {
  console.log('播放')
  if (videoPlayer.value) {
    videoPlayer.value.seek(currentTime.value)
  }
}

// 正在拖动
const videoSeeking = () => {
  console.log('拖动中')
}

// 拖动结束
const videoSeeked = (sliderValue: number) => {
  console.log('拖动完成，进度条:', sliderValue)
}

// 暂停
const videoPause = () => {
  console.log('暂停')
  // showVideo.value = false
  // nextTick(() => {
  //   showVideo.value = true
  // })
}

// 播放结束
const videoEnded = () => {
  console.log('播放结束')
  activeResource.value.viewMinute = activeResource.value.videoMinute
  activeResource.value.viewSecond = activeResource.value.videoSecond
  updateStudyRecord(
    activeResource.value,
    activeResource.value.videoMinute,
    activeResource.value.videoSecond,
  )
}

// 播放错误
const videoError = () => {
  console.error('播放错误')
}
</script>

<style lang="scss" scoped>
.video {
  border-top-left-radius: 14px;
  border-top-right-radius: 14px;
}
</style>
