<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5">
{
  layout: 'inner',
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '练习题',
  },
}
</route>
<template>
  <Card className="flex-1 " content-class="h-full overflow-x-hidden overflow-y-auto relative">
    <view v-for="(ques, qIndex) in examList" :key="ques.ID" class="mb-6">
      <view class="text-base mb-4 flex gap-2 leading-[20px] font-bold">
        <text>{{ qIndex + 1 }}.</text>
        <uv-parse :content="ques.Content"></uv-parse>
      </view>
      <view v-if="ques.Options.length" class="flex flex-wrap gap-5 pl-5">
        <view
          v-for="(option, oIndex) in ques.Options"
          :key="oIndex"
          class="flex items-center gap-2"
        >
          <text class="font-bold">{{ String.fromCharCode(65 + oIndex) }}.</text>
          <uv-parse :content="option"></uv-parse>
        </view>
      </view>
    </view>
    <Loading v-if="loading" />
  </Card>
  <view class="flex items-center justify-end gap-[40px] mt-[18px]">
    <c-button type="primary" :disabled="loading" @click="handlePrint">申请打印</c-button>
  </view>
</template>

<script lang="ts" setup>
import Card from '@/components/common/card.vue'
import CButton from '@/components/common/button.vue'
import Loading from '@/components/common/full-loading.vue'
import { getPaper, printPaper } from '@/service'
defineOptions({
  name: 'Lesson',
})
const examData = ref<any>('')
const examList = ref<any>([])
const resourceId = ref('')
const resourceName = ref('')
const loading = ref(false)
const paperType = ref('')
onLoad((options) => {
  resourceId.value = options.id
  resourceName.value = options.name
  paperType.value = options.paperType
  getPager(options.id, options.paperType)
})
const getPager = async (resourceId, paperType) => {
  try {
    loading.value = true
    const { data } = await getPaper(resourceId, paperType)
    loading.value = false
    examData.value = data.paperContent
    examList.value = JSON.parse(data.paperContent)
    console.log('JSON.parse(data.paperContent)', JSON.parse(data.paperContent))
  } catch (error) {
    loading.value = false
    console.log('error', error)
  }
}
const handlePrint = async () => {
  try {
    await printPaper({
      resourceId: resourceId.value,
      jyeooPaper: examData.value,
      paperType: paperType.value,
    })
    uni.showToast({
      title: '申请成功',
    })
  } catch (error) {
    console.log('error', error)
  }
}
</script>

<style lang="scss"></style>
