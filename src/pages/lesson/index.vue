<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5">
{
  layout: 'default',
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '课程',
  },
}
</route>

<style lang="scss" scoped></style>

<template>
  <view class="flex flex-col h-full relative">
    <view class="w-full overflow-hidden">
      <CateTab :list="cateList" :active-cate="activeCateId" @change="handleCateChange" />
    </view>

    <view class="flex-1 overflow-x-hidden overflow-y-auto relative">
      <view
        class="bg-white rounded-[14px] h-[360px] flex items-stretch overflow-hidden"
        v-if="topData"
      >
        <view
          @click="handleStudy(topData)"
          class="w-[800px] cursor-pointer"
          style="border-right: 1px solid #eee"
        >
          <ImageView
            mode="aspectFill"
            :src="topData.recommendImage"
            class="size-full object-cover"
          />
        </view>
        <view class="flex-1 p-5 flex flex-col justify-between">
          <view>
            <view class="font-bold text-2xl mb-4">{{ topData.courseName }}</view>
            <view class="text-sm text-black/80">
              {{ topData.description }}
            </view>
          </view>
          <view class="mt-auto flex items-center justify-end">
            <view
              class="flex items-center gap-1 text-primary cursor-pointer"
              @click="handleStudy(topData)"
            >
              立刻学习
              <text class="iconfont icon-arrow"></text>
            </view>
          </view>
        </view>
      </view>
      <view class="grid grid-cols-5 gap-[30px] mt-[30px]">
        <LessonItem :item="item" v-for="item in otherList" :key="item.courseId" />
      </view>
    </view>
    <Loading v-if="loading" />
  </view>
</template>

<script lang="ts" setup>
import LessonItem from '@/components/lesson/lesson-item.vue'
import Loading from '@/components/common/full-loading.vue'
import CateTab from '@/components/common/cate-tab.vue'
import { getClassType, getCourse } from '@/service'
import { useConfirm } from '@/hooks/useConfirm'
import ImageView from '@/components/common/image-view.vue'
defineOptions({
  name: 'Lesson',
})

const { confirm } = useConfirm()

const options = ref<Record<string, any>>({})
onLoad((opts) => {
  console.log('onLoad')
  options.value = opts
  geCateList(opts.classType)
  if (!opts.classType) {
    getData()
  }
})
const loading = ref(false)

const cateList = ref<IClassTypeItem[]>([
  {
    typeName: '全部',
    typeId: '',
  },
])
const activeCateId = ref()
const geCateList = async (activeClassType = '') => {
  try {
    const { data } = await getClassType()
    cateList.value = [
      {
        typeName: '全部',
        typeId: '',
      },
      ...data,
    ]
    if (!activeClassType) {
      activeCateId.value = cateList.value[0].typeId
    } else {
      activeCateId.value = cateList.value.find((item) => item.typeName === activeClassType).typeId
      getData()
    }
  } catch (error) {
    console.log('error', error)
  }
}

const topData = ref<ICourseItem>(null)
const otherList = ref<ICourseItem[]>([])
const getData = async () => {
  try {
    loading.value = true
    const { data } = await getCourse({
      typeId: activeCateId.value,
    })
    loading.value = false
    console.log('data', data)
    topData.value = data.top
    otherList.value = data.others
  } catch (error) {
    console.log('error', error)
    loading.value = false
  }
}
function handleCateChange(item) {
  activeCateId.value = item.typeId
  getData()
}
const handleStudy = (item) => {
  if (item.isActivated !== 1) {
    confirm({
      message: '该课程暂未开通，请联系老师开通',
      showCancel: false,
    }).then((res) => {
      //
    })
    return
  }
  uni.navigateTo({
    url: `/pages/lesson/detail?id=${item.courseId}`,
  })
}
</script>
