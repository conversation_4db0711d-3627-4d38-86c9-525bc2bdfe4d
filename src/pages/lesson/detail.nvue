<route lang="json5">
{
  layout: 'none',
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '课程详情',
  },
}
</route>
<template>
  <view class="page-lesson-detail">
    <view class="back-btn" @click="handleBack">
      <image src="/static/images/back.png" style="width: 20px; height: 20px" />
    </view>
    <view class="left-blk">
      <view class="video-blk"></view>
      <view class="info-blk" v-if="activeResource">
        <view class="flex items-center gap-4">
          <view class="text-[28px] font-bold">{{ activeResource.videoResourceName }}</view>
          <text
            class="iconfont icon-fav-fill text-[28px] text-[#ccc] cursor-pointer"
            v-if="activeResource.collectStatus != 1"
            @click="handleFav"
          ></text>
          <text
            class="iconfont icon-fav-fill text-[28px] text-[#FD9F4D] cursor-pointer"
            v-else
          ></text>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import {
  ILessonDetailResourceItem,
  getCourseDetailV2,
  getPlanDetailV2,
  addStudyRecord,
  printPdf,
  getListOssByIds,
} from '@/service'
import { getEnvBaseUrl } from '@/utils'
import type { IResourceItem, ISubjectItem } from '@/typings'
import { ref, reactive, computed, watch, nextTick, onBeforeUnmount } from 'vue'
import { onLoad } from '@dcloudio/uni-app'

const handleBack = () => {
  uni.navigateBack()
}

const loading = ref(false)
const options = ref<Record<string, any>>({})
const allResource = ref<ILessonDetailResourceItem[]>([])
const allSubjects = ref<ISubjectItem[]>([])
const statistic = ref<string>('')
const activeSubject = ref<string>('all')
const activeVersion = ref<string>('all')
const activeTeacher = ref<string>('all')
const activeResource = ref<IResourceItem>(null)

onLoad((opts) => {
  console.log('opts', opts)
  options.value = opts
  if (opts.type === 'plan') {
    uni.setNavigationBarTitle({
      title: '学习计划详情',
    })
  } else if (opts.id) {
    uni.setNavigationBarTitle({
      title: '课程详情',
    })
  }
  initData()
})
// 初始数据
const initData = () => {
  if (options.value.type === 'plan') {
    // 课程查新
    // getPlanDetailData(options.value)
  } else if (options.value.id) {
    // 所有视频查询
    getDetail(options.value.id)
  }
}

// 所有视频查询
const getDetail = (courseId) => {
  try {
    getCourseDetailV2({
      courseId,
    }).then((res) => {
      console.log('res', res)
    })
  } catch (error) {
    console.log('error', error)
  }
}

const chooseModalRef = ref(null)
const handleFav = () => {
  chooseModalRef.value.open(activeResource.value.resourceId)
}
const handleFavSuccess = (item) => {
  activeResource.value.collectStatus = 1
}
</script>

<style lang="scss">
// #ifndef APP-NVUE
page {
  display: flex;
  flex-direction: column;
}
// #endif
.flex-row {
  flex-direction: row;
}
.items-center {
  align-items: center;
}
.text-3xl {
  font-size: 28px;
}
.font-bold {
  font-weight: bold;
}
.back-btn {
  position: absolute;
  top: 10px;
  left: 0;
  z-index: 20;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.page-lesson-detail {
  position: relative;
  flex: 1;
  flex-direction: row;
  padding: 32px 32px 32px 80px;
  background: #f5f5f5;
}
.left-blk {
  width: 639px;
  background: #fff;
  border-radius: 14px;
}
.video-blk {
  height: 360px;
  background: #000;
}
.info-blk {
  padding: 20px;
  position: relative;
}
</style>
