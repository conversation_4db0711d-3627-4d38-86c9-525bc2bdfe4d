<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5">
{
  layout: 'inner',
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '讲义',
  },
  type: 'nvue',
}
</route>
<template>
  <Card className="flex-1 h-full" content-class="handout-content">
    // #ifdef H5
    <PdfViewer :url="pdfUrl" v-if="pdfUrl" />
    // #endif
  </Card>
  <view class="flex items-center justify-end gap-[40px] mt-[18px]">
    <c-button type="primary" @click="handlePrint">申请打印</c-button>
  </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import Card from '@/components/common/card.vue'
import CButton from '@/components/common/button.vue'
import PdfViewer from '@/components/common/pdf-viewer.vue'
import { printPdf } from '@/service'
defineOptions({
  name: 'Lesson',
})

const pdfUrl = ref<string>('')
const resourceId = ref('')
onLoad((options) => {
  console.log('onLoad')
  resourceId.value = options.id
  pdfUrl.value = options.pdf
})

const handlePrint = async () => {
  try {
    await printPdf({
      resourceId: resourceId.value,
      pdfUrl: pdfUrl.value,
    })
    uni.showToast({
      title: '申请成功',
    })
  } catch (error) {
    console.log('error', error)
  }
}
</script>

<style lang="scss" scoped>
.pdf-webview {
  flex: 1;
  width: 100%;
  height: 100vh;
}
</style>
