<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5">
{
  layout: 'login',
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '登录',
  },
}
</route>
<template>
  <view class="w-[560px] flex flex-col items-center gap-[72px]">
    <image src="/static/images/logo.png" class="w-[466px]" mode="widthFix" />
    <uv-form
      labelPosition="left"
      :model="form"
      :rules="rules"
      ref="formRef"
      class="w-full"
      labelWidth="10px"
    >
      <uv-form-item prop="username">
        <uv-input v-model="form.username" placeholder="请输入手机号" clearable shape="circle">
          <template v-slot:prefix>
            <text class="iconfont icon-phone text-[#999]"></text>
          </template>
        </uv-input>
      </uv-form-item>
      <uv-form-item prop="password">
        <uv-input
          v-model="form.password"
          placeholder="请输入密码"
          clearable
          shape="circle"
          :password="!showPassword"
        >
          <template v-slot:prefix>
            <text class="iconfont icon-lock text-[#999] text-2xl"></text>
          </template>
          <template v-slot:suffix>
            <text
              class="iconfont icon-visible text-[#999] text-2xl"
              v-if="showPassword"
              @click="showPassword = false"
            ></text>
            <text
              class="iconfont icon-invisible text-[#999] text-2xl"
              v-else
              @click="showPassword = true"
            ></text>
          </template>
        </uv-input>
      </uv-form-item>
      <view class="flex justify-end mt-4">
        <view>
          <uv-checkbox-group v-model="isRemember" @change="handleRememberChange">
            <uv-checkbox label="记住我" name="1"></uv-checkbox>
          </uv-checkbox-group>
        </view>
      </view>
      <CButton :loading="loading" type="primary" @click="handleSubmit" className="mt-6">
        提交
      </CButton>
    </uv-form>
  </view>
</template>

<script lang="ts" setup>
import { reactive, onMounted } from 'vue'
import CButton from '@/components/common/button.vue'
import { useUserStore } from '@/store'
import { tenantId, clientId, grantType } from '@/config'
import { toast, isNum, formatNum } from '@/utils'
import { queryVersion } from '@/service/index'

// #ifdef APP
// const deviceInfoModule = uni.requireNativePlugin('deviceInfo')
// console.log('deviceInfoModule', deviceInfoModule)
const getDeviceIp = async () => {
  return new Promise((resolve, reject) => {
    if (!plus) {
      return reject(new Error('请在 App-Plus 环境中调用'))
    }
    try {
      // 1. 导入 Context
      const Context = plus.android.importClass('android.content.Context')
      // 2. 拿到当前 Activity
      const activity = plus.android.runtimeMainActivity()
      // 3. 通过 invoke 调用 getSystemService 获取 WIFI 服务
      const wifiManager = plus.android.invoke(activity, 'getSystemService', Context.WIFI_SERVICE)
      // 4. 通过 invoke 拿到 WifiInfo 对象
      const wifiInfo = plus.android.invoke(wifiManager, 'getConnectionInfo')
      // 5. 继续通过 invoke 拿到 int 格式的 IP 地址
      const ipInt = plus.android.invoke(wifiInfo, 'getIpAddress')
      // 6. 转换成点分十进制
      const ip = [
        ipInt & 0xff,
        (ipInt >>> 8) & 0xff,
        (ipInt >>> 16) & 0xff,
        (ipInt >>> 24) & 0xff,
      ].join('.')
      resolve(ip)
    } catch (e) {
      reject(e)
    }
  })
}
const readEth0Mac = async () => {
  return new Promise((resolve, reject) => {
    // 确保在 plusReady 后执行
    if (!plus) {
      return reject(new Error('请在 App-Plus 环境下调用'))
    }
    try {
      // 导入 Java 原生类
      const FileReader = plus.android.importClass('java.io.FileReader')
      const BufferedReader = plus.android.importClass('java.io.BufferedReader')

      // 打开文件
      const fr = new FileReader('/sys/class/net/eth0/address')
      const br = new BufferedReader(fr)

      // 读取第一行（MAC 地址通常在首行）
      const mac = br.readLine()

      // 关闭流
      br.close()
      fr.close()

      resolve(mac.trim())
    } catch (e) {
      reject(e)
    }
  })
}
const mac = ref('')
onMounted(async () => {
  try {
    mac.value = (await readEth0Mac()) as string
    userStore.setMac(mac.value)
    // console.log('mac', mac)
    const ip = await getDeviceIp()
    // console.log('设备 IP:', ip)
  } catch (error) {
    console.log('error', error)
  }
})

// #endif
const userStore = useUserStore()

defineOptions({
  name: 'Login',
})

// const form = ref({
//   username: '18753191437',
//   password: '191437',
// })
const form = ref({
  username: uni.getStorageSync('username') || '',
  password: uni.getStorageSync('password') || '',
})
const showPassword = ref(false)
const loading = ref(false)
const formRef = ref(null)
const rules = {
  username: {
    type: 'string',
    required: true,
    message: '请填写姓名',
    trigger: ['blur', 'change'],
  },
  password: {
    type: 'string',
    required: true,
    message: '请填写密码',
    trigger: ['blur', 'change'],
  },
}
const isRemember = ref(uni.getStorageSync('rememberMe') ? ['1'] : [])
const handleRememberChange = (e) => {
  console.log('handleRememberChange', e)
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    const rememberMe = isRemember.value.length > 0

    loading.value = true
    const formData = {
      ...form.value,
      tenantId,
      clientId,
      grantType,
      rememberMe,
      // #ifdef APP
      macAddr: mac.value,
      // #endif
    }
    console.log('formData', formData)
    await userStore.login(formData)
    loading.value = false
    if (rememberMe) {
      uni.setStorageSync('username', form.value.username)
      uni.setStorageSync('password', form.value.password)
      uni.setStorageSync('rememberMe', 1)
    } else {
      uni.removeStorageSync('username')
      uni.removeStorageSync('password')
      uni.removeStorageSync('rememberMe')
    }
    uni.redirectTo({
      url: '/pages/home/<USER>',
    })
  } catch (error) {
    console.log('验证失败', error)
    loading.value = false
  }
}

// #ifdef APP-PLUS
const isUpdating = ref(false) // ✅ 防重入锁
function installNew(data) {
  console.log(data.url)
  if (showUpdate.value) return
  showUpdate.value = true
  uni.showModal({
    title: '新版本发布',
    content: '检查到当前有新版本' + data.version + ',需要更新吗？',
    confirmText: '立即更新',
    showCancel: !data.isForceUpgrade,
    success: function (res) {
      if (res.confirm) {
        if (isUpdating.value) return
        isUpdating.value = true
        showUpdate.value = false
        if (data.url.indexOf('.apk') > -1) {
          plus.runtime.openURL(data.url)
          return false
        }
        uni.showLoading({
          title: '正在下载',
        })
        uni.downloadFile({
          url: data.url,
          success: (downloadResult) => {
            if (downloadResult.statusCode === 200) {
              uni.showLoading({
                title: '安装中..',
              })
              plus.runtime.install(
                downloadResult.tempFilePath,
                {
                  force: false,
                },
                function () {
                  uni.hideLoading()
                  uni.showModal({
                    title: '提示',
                    content: `APP版本 ${data.version} 升级成功`,
                    confirmText: '重启应用',
                    showCancel: false,
                    success: function (res) {
                      plus.runtime.restart()
                    },
                  })
                },
                function (e) {
                  uni.hideLoading()
                  uni.showModal({
                    showCancel: false,
                    title: '安装失败',
                    content: e.message || '安装失败',
                  })
                },
              )
            } else {
              isUpdating.value = false // ❗下载失败也要解锁
            }
          },
          fail() {
            uni.hideLoading()
            isUpdating.value = false
            uni.showToast({ title: '下载失败', icon: 'none' })
          },
        })
      } else if (res.cancel) {
        console.log('用户点击取消')
        showUpdate.value = false
      }
    },
  })
}
function getVersionNum(str) {
  const temp = str.split('.')
  let majorVersion = temp[0] || '0'
  let minorVersion = temp[1] || '0'
  let patchVersion = temp[2] || '0'
  if (!isNum(majorVersion)) {
    majorVersion = '0'
  }
  if (!isNum(minorVersion)) {
    minorVersion = '0'
  }
  if (!isNum(patchVersion)) {
    patchVersion = '0'
  }
  const version = `${formatNum(majorVersion)}${formatNum(minorVersion)}${formatNum(patchVersion)}`
  return parseInt(version)
}
const showUpdate = ref(false)
function checkUpdate() {
  const systemInfo = uni.getSystemInfoSync()
  const platform = systemInfo.platform
  if (platform !== 'android') return
  // console.log('checkUpdate', plus.runtime.appid)
  plus.runtime.getProperty(plus.runtime.appid, async (widgetInfo) => {
    console.log('widgetInfo', widgetInfo)
    try {
      const versoionRes = await queryVersion()
      const versionData = versoionRes.data
      console.log('versionData', versionData)
      if (!versionData || !versionData.boxAppVersion) return
      const newVersion = getVersionNum(versionData.boxAppVersion)
      const appVersion = widgetInfo.version
      const currentVersion = getVersionNum(appVersion)
      console.log('currentVersion', currentVersion, 'newVersion', newVersion)
      // console.log('newVersion', newVersion)
      if (newVersion <= currentVersion) return
      installNew({
        version: versionData.boxAppVersion,
        url: versionData.boxAppUrl,
        isForceUpgrade: versionData.isForceUpgrade,
      })
    } catch (e) {
      console.log(e)
    }
  })
}
onShow(() => {
  console.log('login show')
  checkUpdate()
})
// #endif
</script>

<style></style>
