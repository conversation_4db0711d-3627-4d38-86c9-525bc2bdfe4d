<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5">
{
  layout: 'default',
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '错题本',
  },
}
</route>
<template>
  <view class="flex flex-col h-full relative">
    <SubjectTab
      :list="subjectList"
      :active-id="activeSubject.subjectId"
      @change="handleSubjectChange"
    />
    <view class="flex-1 overflow-x-hidden overflow-y-auto relative">
      <NoData class="mt-10" v-if="!dataList.length" />
      <view class="flex flex-col gap-6">
        <navigator
          :url="`/pages/mistake/detail?paperId=${item.paperId}&subjectCode=${activeSubject.subjectCode}`"
          v-for="item in dataList"
          :key="item.paperId"
          class="bg-[#E2E9F2] rounded-3 cursor-pointer"
        >
          <view class="text-base text-black/60 px-6 py-2">{{ item.createTime || '--' }}</view>
          <view class="bg-white rounded-3 px-6 py-4">
            <mp-html
              :selectable="false"
              containerStyle="display:flex;flex-wrap:wrap;"
              :tag-style="tagStyle"
              :content="item.content"
            ></mp-html>
          </view>
        </navigator>
      </view>
    </view>
    <Loading v-if="loading" />
  </view>
</template>

<script lang="ts" setup>
import SubjectTab from '@/components/mistake/subject-tab.vue'
import Loading from '@/components/common/full-loading.vue'
import { getSubject, getMistake } from '@/service'
import NoData from '@/components/common/no-data.vue'
import mpHtml from 'mp-html/dist/uni-app/components/mp-html/mp-html'

defineOptions({
  name: 'Lesson',
})

const tagStyle = {
  div: `display: flex; flex-direction:column; align-items:center;justify-content:center;`,
  table: `display: block; vertical-align:middle`,
  td: `padding:0;`,
  tr: 'display:flex; align-items:center;',
}
const subjectList = ref<ISubjectItem[]>([])
const getSubjectData = async () => {
  try {
    const { data } = await getSubject()
    subjectList.value = data
    activeSubject.value = data[0]

    getData()
  } catch (error) {
    console.log('error', error)
  }
}
getSubjectData()
const loading = ref(false)
const activeSubject = ref(null)
function handleSubjectChange(item) {
  activeSubject.value = item
  getData()
}

const dataList = ref<IMistakeItem[]>([])
const getData = () => {
  loading.value = true
  getMistake({ subjectId: activeSubject.value.subjectId })
    .then((res) => {
      dataList.value = res.data
    })
    .finally(() => {
      loading.value = false
    })
}
</script>

<style lang="scss">
.quizPutTag {
  display: inline;
  padding: 0 20px;
  border-bottom: 1px solid #333;
}
</style>
