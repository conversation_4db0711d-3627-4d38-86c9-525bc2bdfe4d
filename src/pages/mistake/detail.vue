<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5">
{
  layout: 'inner',
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '错题本详情',
  },
}
</route>
<template>
  <Card className="flex-1 " content-class="h-full overflow-x-hidden overflow-y-auto">
    <view class="text-base mb-4 flex gap-2 leading-[20px] font-bold">
      <mp-html
        containerStyle="display:flex;flex-wrap:wrap;"
        :selectable="false"
        :tag-style="tagStyle"
        :content="data.content"
      ></mp-html>
    </view>
    <view v-if="data.optionVoList && data.optionVoList.length" class="flex flex-wrap gap-5">
      <view
        v-for="(option, oIndex) in data.optionVoList"
        :key="oIndex"
        class="flex items-center gap-2"
      >
        <text class="font-bold">{{ String.fromCharCode(65 + oIndex) }}.</text>
        <mp-html
          containerStyle="display:flex;flex-wrap:wrap;"
          :selectable="false"
          :content="option.paperOption"
        ></mp-html>
      </view>
    </view>
    <view class="text-[#228B22] font-bold mt-5">
      解析：
      <template v-if="data.optionVoList">
        <text v-if="data.answers">{{ String.fromCharCode(65 + parseInt(data.answers)) }}</text>
        <text v-else>--</text>
      </template>
      <template v-else>
        <uv-parse v-if="data.answers" :content="data.answers"></uv-parse>
        <text v-else>--</text>
      </template>
    </view>
    <view class="font-bold mt-5">【分析】</view>
    <uv-parse v-if="data.analyse" :content="data.analyse"></uv-parse>
    <text v-else>--</text>
    <view class="font-bold mt-5">【详解】</view>
    <uv-parse v-if="data.discuss" :content="data.discuss"></uv-parse>
    <text v-else>--</text>
  </Card>
  <view class="flex items-center justify-end gap-[40px] mt-[18px]">
    <c-button type="primary" @click="handleStudy">知识点学习</c-button>
    <c-button type="primary" @click="handleThink">举一反三</c-button>
  </view>
</template>

<script lang="ts" setup>
import Card from '@/components/common/card.vue'
import CButton from '@/components/common/button.vue'
import { getMistakeDetail } from '@/service'
import mpHtml from 'mp-html/dist/uni-app/components/mp-html/mp-html'
defineOptions({
  name: 'Lesson',
})

const tagStyle = {
  div: `display: flex; flex-direction:column; align-items:center;justify-content:center;`,
  table: `display: flex; vertical-align:middle`,
  td: `padding:0`,
  tr: 'display:flex; align-items:center;',
}
const subjectCode = ref('')
onLoad((options) => {
  console.log('onLoad')
  subjectCode.value = options.subjectCode
  getData(options.paperId)
})
const loading = ref(false)
const data = ref(null)
const getData = (paperId) => {
  loading.value = true
  getMistakeDetail({ paperId })
    .then((res) => {
      data.value = res.data
    })
    .finally(() => {
      loading.value = false
    })
}
const handleStudy = () => {
  uni.navigateTo({
    url: `/pages/lesson/detail?id=${data.value.courseId}&resourceId=${data.value.resourceId}`,
  })
}
const handleThink = () => {
  uni.navigateTo({
    url: `/pages/mistake/think?paperId=${data.value.sid}&subjectId=${data.value.subjectId}&subjectCode=${subjectCode.value}&resourceId=${data.value.resourceId}`,
  })
}
</script>

<style lang="scss" scoped></style>
