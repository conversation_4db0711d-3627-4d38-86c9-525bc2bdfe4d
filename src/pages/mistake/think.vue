<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5">
{
  layout: 'inner',
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '举一反三',
  },
}
</route>
<template>
  <Card className="flex-1 " content-class="h-full overflow-x-hidden overflow-y-auto relative">
    <view v-for="(ques, qIndex) in examList" :key="ques.ID" class="mb-10">
      <view class="text-base mb-4 flex gap-2 leading-[20px] font-bold">
        <text>{{ qIndex + 1 }}.</text>
        <uv-parse :content="ques.Content"></uv-parse>
      </view>
<!--      <view v-if="ques.Options.length" class="flex flex-wrap gap-5 pl-5">-->
<!--        <view-->
<!--          v-for="(option, oIndex) in ques.Options"-->
<!--          :key="oIndex"-->
<!--          class="flex items-center gap-2"-->
<!--        >-->
<!--          <text class="font-bold">{{ String.fromCharCode(65 + oIndex) }}.</text>-->
<!--          <uv-parse :content="option"></uv-parse>-->
<!--        </view>-->
<!--      </view>-->
<!--      <view class="text-[#228B22] font-bold mt-5">-->
<!--        解析：-->
<!--        <text v-if="ques.DisplayAnswer">{{ ques.DisplayAnswer }}</text>-->
<!--        <text v-else>&#45;&#45;</text>-->
<!--      </view>-->
<!--      <view class="font-bold mt-5">【分析】</view>-->
<!--      <uv-parse v-if="ques.Analyse" :content="ques.Analyse"></uv-parse>-->
<!--      <text v-else>&#45;&#45;</text>-->
<!--      <view class="font-bold mt-5">【详解】</view>-->
<!--      <uv-parse v-if="ques.Discuss" :content="ques.Discuss"></uv-parse>-->
<!--      <text v-else>&#45;&#45;</text>-->
<!--      <uv-parse v-if="ques.Method" :content="ques.Method"></uv-parse>-->
<!--      <text v-else>&#45;&#45;</text>-->
    </view>
    <Loading v-if="loading" />
  </Card>
  <view class="flex items-center justify-end gap-[40px] mt-[18px]">
    <c-button type="primary" :disabled="loading || !jyeooAuthInfo" @click="handlePrint">
      申请打印
    </c-button>
  </view>
</template>

<script lang="ts" setup>
import Card from '@/components/common/card.vue'
import CButton from '@/components/common/button.vue'
import Loading from '@/components/common/full-loading.vue'
import { quesSame, getAnalysis } from '@/service/jyeoo'
import { applyPrintThink } from '@/service'
import { useUserStore } from '@/store'
import { storeToRefs } from 'pinia'
const userStore = useUserStore()
const { jyeooAuthInfo } = storeToRefs(userStore)
defineOptions({
  name: 'Lesson',
})
const examData = ref<any>('')
const examList = ref<any>([])
const resourceId = ref('')
const subjectCode = ref('')
const loading = ref(false)
onLoad((options) => {
  resourceId.value = options.resourceId
  subjectCode.value = options.subjectCode
  getPager(options.subjectId, options.paperId)
})
const getPager = async (subjectId, paperId) => {
  if (!jyeooAuthInfo.value) {
    uni.showToast({
      title: '菁优登录失败，请重新登录',
      icon: 'none',
    })
    return
  }
  try {
    loading.value = true
    const res = await quesSame(subjectCode.value, {
      subject: subjectId,
      id: paperId,
      ps: 3,
    })
    loading.value = false
    // examList.value = res
    const sIds = res.map((item) => item.SID)
    const analysisRes = await getAnalysis(subjectCode.value, {
      subject: subjectId,
      id: sIds.join(','),
    })
    if (analysisRes && analysisRes.length > 0) {
      examList.value = analysisRes
      examData.value = JSON.stringify(analysisRes)
    } else {
      examList.value = res
      examData.value = JSON.stringify(res)
    }
    // console.log('analysis', analysis)
  } catch (error) {
    loading.value = false
    console.log('error', error)
  }
}
const handlePrint = async () => {
  try {
    await applyPrintThink({
      resourceId: resourceId.value,
      jyeooPaper: examData.value,
    })
    uni.showToast({
      title: '申请成功',
    })
  } catch (error) {
    console.log('error', error)
  }
}
</script>

<style lang="scss"></style>
