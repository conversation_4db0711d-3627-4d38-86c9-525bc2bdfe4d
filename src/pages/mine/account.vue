<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5">
{
  layout: 'inner',
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '我的账户',
  },
}
</route>
<template>
  <view class="text-center font-medium text-[36px] mb-5">我的账户</view>
  <view class="flex-1 overflow-y-auto overflow-x-hidden">
    <view class="text-[28px] font-bold">基本信息</view>
    <view class="px-5 flex flex-col gap-4 text-[22px] mt-6">
      <view>
        <text>姓名：</text>
        <text>{{ userInfo?.nickName || userInfo?.userName || '--' }}（1720513）</text>
      </view>
      <view>
        <text>学校：</text>
        <text>{{ userInfo?.deptName || '--' }}</text>
      </view>
      <view>
        <text>电话：</text>
        <text>
          {{
            userInfo?.phonenumber ||
            userInfo?.parentPhoneFirst ||
            userInfo?.parentPhoneSecond ||
            '--'
          }}
        </text>
      </view>
      <view>
        <text>MAC：</text>
        <text>{{ mac || '--' }}</text>
      </view>
    </view>
    <view class="text-[28px] font-bold mt-[40px]">我的课程</view>
    <view class="grid grid-cols-5 gap-[30px] mt-[24px]">
      <LessonItem v-for="item in myCourse" :key="item.courseId" :item="item" show-time />
    </view>
  </view>
</template>

<script lang="ts" setup>
import LessonItem from '@/components/lesson/lesson-item.vue'
import { useUserStore } from '@/store'
import { storeToRefs } from 'pinia'
import { getMyCourse } from '@/service'
defineOptions({
  name: 'Account',
})
const userStore = useUserStore()
const { userInfo, mac } = storeToRefs(userStore)

const myCourse = ref<ICourseItem[]>([])
const getMyCourseData = async () => {
  try {
    const { data } = await getMyCourse()
    myCourse.value = data.course
  } catch (error) {
    console.log('error', error)
  }
}
getMyCourseData()
</script>

<style lang="scss" scoped></style>
