<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5">
{
  layout: 'inner',
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '学习记录',
  },
}
</route>
<template>
  <view class="text-center font-medium text-[36px] mb-5">学习记录</view>
  <view class="flex-1 overflow-y-auto overflow-x-hidden">
    <template v-for="item in studyRecord" :key="item">
      <view class="text-[28px] font-bold">{{ item.date }}</view>
      <view class="grid grid-cols-5 gap-[30px] my-[24px]">
        <LessonItem v-for="cItem in item.course" :key="cItem.courseId" :item="cItem" show-time />
      </view>
    </template>
  </view>
</template>

<script lang="ts" setup>
import LessonItem from '@/components/lesson/lesson-item.vue'
import { getStudyRecord } from '@/service'
defineOptions({
  name: 'Lesson',
})

const studyRecord = ref<{ date: string; course: ICourseItem[] }[]>([])
const getStudyRecordData = async () => {
  try {
    const { data } = await getStudyRecord()
    studyRecord.value = data
  } catch (error) {
    console.log('error', error)
  }
}
getStudyRecordData()
</script>

<style lang="scss" scoped></style>
