<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5">
{
  layout: 'inner',
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '我的收藏',
  },
}
</route>
<template>
  <view class="h-full flex items-stretch gap-6">
    <Favorite
      @change="handleFavChange"
      @add="getFavoriteData"
      @delete="getFavoriteData"
      :list="foldList"
    />
    <Card class-name="flex-1" content-class="flex-1 overflow-y-auto overflow-x-hidden">
      <view class="flex flex-col gap-6">
        <NoData v-if="!dataList.length" />
        <view
          v-for="item in dataList"
          :key="item.resourceId"
          class="bg-[#e5e5e5] rounded-[6px] px-6 h-[72px] shrink-0 flex gap-4 items-center justify-between"
        >
          <view
            class="flex-1 cursor-pointer text-black/60 hover:text-primary text-xl"
            @click="handleItemClick(item)"
          >
            {{ item.videoResourceName }}
          </view>
          <view class="text-black/60" v-if="item.viewMinute > 0 || item.viewSecond > 0">
            已学习{{ item.viewMinute || 0 }}分{{ item.viewSecond || 0 }}秒
          </view>
          <view
            class="flex items-center justify-center rounded-full size-[28px] cursor-pointer text-black/60 hover:text-primary"
            @click="handleDelete(item)"
          >
            <text class="iconfont icon-delete text-[16px]"></text>
          </view>
        </view>
      </view>
    </Card>
  </view>
</template>

<script lang="ts" setup>
import Favorite from '@/components/lesson/favorite.vue'
import Card from '@/components/common/card.vue'
import { useConfirm } from '@/hooks/useConfirm'
import { getFavorite, deleteFavorite } from '@/service'
import NoData from '@/components/common/no-data.vue'
defineOptions({
  name: 'MyCollection',
})
const { confirm } = useConfirm()

const loading = ref(false)
const foldList = ref<IFavoriteFoldItem[]>([])
const getFavoriteData = async () => {
  try {
    const { data } = await getFavorite()
    foldList.value = data
  } catch (error) {
    console.log('error', error)
  }
}
getFavoriteData()
const dataList = ref<IResourceItem[]>([])

const handleFavChange = (item) => {
  console.log('handleFavChange', item)
  dataList.value = item.resourceVos || []
}
const handleItemClick = (item) => {
  console.log('handleItemClick', item)
  uni.navigateTo({
    url: `/pages/lesson/detail?id=${item.courseId}&resourceId=${item.resourceId}`,
  })
}
const handleDelete = (item) => {
  console.log('handleDelete', item)
  confirm({
    message: `确定要取消收藏课程《${item.videoResourceName}》吗？`,
  }).then((res) => {
    if (res) {
      deleteFavorite(item.recordId).then(() => {
        getFavoriteData()
        handleFavChange(item)
      })
    }
  })
}
</script>

<style lang="scss" scoped></style>
