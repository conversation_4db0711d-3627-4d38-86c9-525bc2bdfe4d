<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5">
{
  layout: 'default',
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '我的',
  },
}
</route>
<template>
  <view class="h-full flex flex-col">
    <view class="flex items-center justify-between">
      <view class="text-[28px] font-bold">
        {{ userInfo.nickName || userInfo.userName || '--' }}，你好
      </view>
      <view class="relative group">
        <WhiteButton text="设置" />
        <view class="absolute right-0 top-[36px] hidden group-hover:block">
          <view class="bg-white w-[130px] px-5 py-2 rounded-[14px] mt-1">
            <view
              @click="skipTo('/pages/mine/password')"
              class="link flex items-center justify-start gap-2 h-[43px]"
            >
              <text class="iconfont icon-lock-outline text-[16px]"></text>
              <text class="whitespace-nowrap hover:text-primary">修改账号</text>
            </view>
            <view
              class="link flex items-center justify-start gap-2 h-[43px] whitespace-nowrap"
              @click="handleExit"
            >
              <text class="iconfont icon-exit text-[16px]"></text>
              <text class="whitespace-nowrap hover:text-primary">退出账号</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="flex items-center gap-4 mt-5">
      <WhiteButton
        text="账户信息"
        icon="icon-user"
        icon-bg="#DE6FFF"
        @click="skipTo('/pages/mine/account')"
      />
      <WhiteButton
        text="学习计划"
        icon="icon-plan"
        icon-bg="#786FFF"
        @click="skipTo('/pages/plan/index')"
      />
      <WhiteButton
        text="学习记录"
        icon="icon-user"
        icon-bg="#56BD60"
        @click="skipTo('/pages/mine/history')"
      />
      <WhiteButton
        text="我的收藏"
        icon="icon-fav"
        icon-bg="#FD9F4D"
        @click="skipTo('/pages/mine/collect')"
      />
    </view>
    <view class="flex items-end gap-4 mt-8">
      <view class="text-[28px] font-bold leading-none">我的课程</view>
      <view class="text-black/60">
        你已累计学习
        <text class="text-primary text-2xl font-bold">{{ total.hours }}</text>
        小时
        <text class="text-primary text-2xl font-bold">{{ total.minutes }}</text>
        分钟
      </view>
    </view>
    <view class="flex-1 overflow-x-hidden overflow-y-auto mt-6">
      <view class="grid grid-cols-5 gap-[30px]">
        <LessonItem v-for="item in myCourse" :key="item.courseId" :item="item" show-time />
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import LessonItem from '@/components/lesson/lesson-item.vue'
import WhiteButton from '@/components/common/white-button.vue'
import { useConfirm } from '@/hooks/useConfirm'
import { useUserStore } from '@/store'
import { storeToRefs } from 'pinia'
import { getMyCourse } from '@/service'

const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)
const { confirm } = useConfirm()
defineOptions({
  name: 'Lesson',
})
function parseStudyTime(sentence: string): { hours: string; minutes: string } {
  const regex = /(\d+)\s*小时\s*(\d+)\s*分钟/
  const match = sentence.match(regex)
  if (!match) {
    throw new Error('无法解析学习时长')
  }
  const [, hoursStr, minutesStr] = match
  const hours = parseInt(hoursStr, 10) + ''
  const minutes = parseInt(minutesStr, 10) + ''
  return { hours, minutes }
}
const myCourse = ref<ICourseItem[]>([])
const total = ref<{ hours: string; minutes: string }>({ hours: '0', minutes: '0' })
const getMyCourseData = async () => {
  try {
    const { data } = await getMyCourse()
    myCourse.value = data.course
    total.value = parseStudyTime(data.total)
  } catch (error) {
    console.log('error', error)
  }
}
getMyCourseData()

const skipTo = (url) => {
  uni.navigateTo({
    url,
  })
}

const handleExit = () => {
  confirm({
    message: '确定要退出吗？',
  }).then((res) => {
    if (res) {
      userStore.logout()
    }
  })
}
</script>

<style lang="scss" scoped>
.link {
  cursor: pointer;
  &:hover {
    color: $uv-primary;
  }
}
</style>
