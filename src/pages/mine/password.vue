<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5">
{
  layout: 'login',
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '修改密码',
  },
}
</route>
<template>
  <view class="absolute top-0 left-0 w-full flex items-center justify-center h-[100px]">
    <view
      class="absolute top-0 left-0 size-[80px] flex items-center justify-center cursor-pointer"
      @click="handleBack"
    >
      <text class="iconfont icon-back text-[20px]"></text>
    </view>
    <text class="font-medium text-[36px]">修改密码</text>
  </view>
  <view class="w-[560px] flex flex-col items-center gap-[72px]">
    <uv-form
      labelPosition="left"
      :model="form"
      :rules="rules"
      ref="formRef"
      class="w-full"
      labelWidth="10px"
    >
      <uv-form-item prop="oldPassword">
        <uv-input
          v-model="form.oldPassword"
          placeholder="请输入原密码"
          clearable
          shape="circle"
          :password="!showPassword"
        >
          <template v-slot:prefix>
            <text class="iconfont icon-lock text-[#999] text-2xl"></text>
          </template>
          <template v-slot:suffix>
            <text
              class="iconfont icon-visible text-[#999] text-2xl"
              v-if="showPassword"
              @click="showPassword = false"
            ></text>
            <text
              class="iconfont icon-invisible text-[#999] text-2xl"
              v-else
              @click="showPassword = true"
            ></text>
          </template>
        </uv-input>
      </uv-form-item>
      <uv-form-item prop="newPassword">
        <uv-input
          v-model="form.newPassword"
          placeholder="请输入新密码"
          clearable
          shape="circle"
          :password="!showPassword1"
        >
          <template v-slot:prefix>
            <text class="iconfont icon-lock text-[#999] text-2xl"></text>
          </template>
          <template v-slot:suffix>
            <text
              class="iconfont icon-visible text-[#999] text-2xl"
              v-if="showPassword1"
              @click="showPassword1 = false"
            ></text>
            <text
              class="iconfont icon-invisible text-[#999] text-2xl"
              v-else
              @click="showPassword1 = true"
            ></text>
          </template>
        </uv-input>
      </uv-form-item>
      <uv-form-item prop="confirmPwd">
        <uv-input
          v-model="form.confirmPwd"
          placeholder="请再次输入新密码"
          clearable
          shape="circle"
          :password="!showPassword2"
        >
          <template v-slot:prefix>
            <text class="iconfont icon-lock text-[#999] text-2xl"></text>
          </template>
          <template v-slot:suffix>
            <text
              class="iconfont icon-visible text-[#999] text-2xl"
              v-if="showPassword2"
              @click="showPassword2 = false"
            ></text>
            <text
              class="iconfont icon-invisible text-[#999] text-2xl"
              v-else
              @click="showPassword2 = true"
            ></text>
          </template>
        </uv-input>
      </uv-form-item>
      <CButton :loading="loading" type="primary" @click="handleSubmit" className="mt-6">
        提交
      </CButton>
    </uv-form>
  </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import CButton from '@/components/common/button.vue'
import { updatePwd } from '@/service'
import { useUserStore } from '@/store'

defineOptions({
  name: 'Home',
})

onLoad(() => {
  console.log('onLoad')
})

const form = ref({
  oldPassword: '',
  newPassword: '',
  confirmPwd: '',
})
const showPassword = ref(false)
const showPassword1 = ref(false)
const showPassword2 = ref(false)
const loading = ref(false)
const formRef = ref(null)
const rules = {
  oldPassword: {
    type: 'string',
    required: true,
    message: '请填写原密码',
    trigger: ['blur', 'change'],
  },
  newPassword: {
    type: 'string',
    required: true,
    message: '请填写新密码',
    trigger: ['blur', 'change'],
  },
  confirmPwd: [
    {
      type: 'string',
      required: true,
      message: '请再次填写新密码',
      trigger: ['blur', 'change'],
    },
    {
      // 自定义 validator, 触发时机同样用 blur（或 input）
      validator: (rule, value, callback) => {
        if (value !== form.value.newPassword) {
          callback(new Error('两次密码不一致'))
        } else {
          callback()
        }
      },
      trigger: ['blur', 'change'],
    },
  ],
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    console.log('验证通过')
    loading.value = true
    await updatePwd({
      oldPassword: form.value.oldPassword,
      newPassword: form.value.newPassword,
    })
    loading.value = false
    uni.showToast({
      icon: 'none',
      title: '修改成功，请重新登录',
    })
    setTimeout(() => {
      const userStore = useUserStore()
      userStore.logout()
      uni.reLaunch({
        url: '/pages/login/index',
      })
    }, 2000)
  } catch (error) {
    console.log('验证失败', error)
    loading.value = false
  }
}
const handleBack = () => {
  uni.navigateBack()
}
</script>

<style></style>
