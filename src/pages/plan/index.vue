<route lang="json5">
{
  layout: 'default',
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '学习计划',
  },
}
</route>
<template>
  <view class="h-full overflow-auto">
    <Calendar
      :events="eventList"
      @event-click="handleEventClick"
      @month-change="handleMonthChange"
    />
  </view>
</template>

<script lang="ts" setup>
import Calendar from '@/components/plan/calendar.vue'
import { getPlan, getPlanDetail } from '@/service'

defineOptions({
  name: 'Plan',
})
const month = ref('')
const handleMonthChange = (val) => {
  console.log('handleMonthChange', val)
  month.value = val
  getPlanData()
}
const eventList = ref<IPlanItem[]>([])
const getPlanData = async () => {
  if (!month.value) return
  try {
    const { data } = await getPlan({
      month: month.value,
    })
    console.log('data', data)
    const list = []
    data.forEach((item) => {
      item.forEach((planItem) => {
        if (planItem.plans && planItem.plans.length > 0) {
          list.push(planItem)
        }
      })
    })
    console.log(list)
    eventList.value = list
  } catch (error) {
    console.log('error', error)
  }
}

const handleEventClick = (evt) => {
  console.log('event-click', evt)
  uni.navigateTo({
    url: `/pages/lesson/detail?type=plan&resourceId=${evt.resourceId}&startDate=${evt.startTime.split(' ')[0]}&endDate=${evt.endTime.split(' ')[0]}`,
  })
  // getPlanDetail({ startTime: evt.startTime, endTime: evt.endTime })
}
</script>

<style lang="scss" scoped></style>
