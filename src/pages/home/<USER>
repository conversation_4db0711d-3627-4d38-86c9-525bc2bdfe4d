<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5" type="home">
{
  layout: 'default',
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '首页',
  },
}
</route>
<template>
  <view class="py-8 pr-16 flex gap-10 size-full overflow-auto">
    <view class="flex flex-col gap-8 w-[25%]">
      <navigator
        class="relative flex-1 bg-white rounded-4 flex flex-col items-center justify-center text-primary text-xl text-center font-500 bg-no-repeat"
        :style="{
          backgroundImage: `url(${item.bg})`,
          backgroundPosition: 'bottom right',
          backgroundSize: 'auto 100%',
        }"
        :url="item.url"
        v-for="(item, index) in menuList"
        :key="index"
      >
        <view class="icon flex items-center justify-center mb-3 h-[46px]">
          <image v-if="item.img" mode="widthFix" :src="item.img" class="w-[200px]" />
          <text v-if="item.icon" :class="[item.icon, 'text-[46px]']"></text>
        </view>
        <text>{{ item.name }}</text>
      </navigator>
    </view>
    <view class="flex-1 flex flex-col gap-10 h-full">
      <view
        class="flex-1 w-full overflow-hidden bg-white rounded-[14px]"
        v-if="recommend && recommend.image"
      >
        <image-view mode="aspectFill" radius="14" :src="recommend.image" class="w-full h-full" />
      </view>
      <view class="grid grid-cols-3 gap-[30px] h-[205px] shrink-0">
        <navigator
          :url="`/pages/lesson/detail?id=${item.courseId}`"
          class="flex-1 bg-white rounded-[14px]"
          v-for="item in otherList"
          :key="item.courseId"
        >
          <image-view
            mode="aspectFill"
            :src="item.detailImage"
            radius="14"
            class="w-full h-[205px]"
          />
        </navigator>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import aboutBg from '@/static/images/home-about.png'
import aiBg from '@/static/images/home-ai.png'
import lessonBg from '@/static/images/home-lesson.png'
import increaseBg from '@/static/images/home-increase.png'
import { getHomeCourse } from '@/service'
import ImageView from '@/components/common/image-view.vue'

defineOptions({
  name: 'Home',
})

const menuList = [
  {
    name: '了解聪学伴学',
    img: '/static/images/logo.png',
    bg: aboutBg,
    url: '/pages/home/<USER>',
  },
  {
    name: 'AI课表',
    icon: 'iconfont icon-plan',
    bg: aiBg,
    url: '/pages/plan/index',
  },
  {
    name: '课程中心',
    icon: 'iconfont icon-video',
    bg: lessonBg,
    url: '/pages/lesson/index',
  },
  {
    name: '拔高提升',
    icon: 'iconfont icon-increase',
    bg: increaseBg,
    url: '/pages/lesson/index?classType=同步拔高课',
  },
]
const recommend = ref<{ image: string }>(null)
const otherList = ref<ICourseItem[]>([])
const getData = async () => {
  try {
    const { data } = await getHomeCourse()
    const { top, others } = data
    recommend.value = { image: top.images[0] }
    otherList.value = others || []
  } catch (error) {
    console.log('error', error)
  }
}
getData()
</script>

<style></style>
