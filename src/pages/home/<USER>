<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5">
{
  layout: 'inner',
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '了解聪学伴学',
  },
}
</route>
<template>
  <view class="text-center font-medium text-[36px] mb-5">了解聪学伴学</view>
  <Card className="flex-1 " content-class="h-full overflow-x-hidden overflow-y-auto leading-loose">
    <uv-parse :content="content"></uv-parse>
  </Card>
</template>

<script lang="ts" setup>
import Card from '@/components/common/card.vue'
import { getAbout } from '@/service'
defineOptions({
  name: 'About',
})

const content = ref('')
const getContent = async () => {
  try {
    const { data } = await getAbout()
    content.value = data.usContent
  } catch (error) {
    console.log('error', error)
  }
}
getContent()
</script>

<style lang="scss" scoped></style>
